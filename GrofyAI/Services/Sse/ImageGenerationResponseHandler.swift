import Foundation

// MARK: - 图像生成响应处理器

enum ImageGenerationResponseHandler {
    private static let sharedDecoder: JSONDecoder = {
        let decoder = JSONDecoder()
        return decoder
    }()

    static func parseImageGenerationResponse(from jsonString: String) -> Res<ImageGenerationResponse>? {
        let trimmedString = jsonString.trimmingCharacters(in: .whitespaces)

        guard !trimmedString.isEmpty else { return nil }

        guard let jsonData = trimmedString.data(using: .utf8) else {
            print("ImageGenerationResponseHandler: 数据转换失败: \(trimmedString)")
            return nil
        }

        if let directResponse = try? Self.sharedDecoder.decode(Res<ImageGenerationResponse>.self, from: jsonData) {
            return directResponse
        }

        if let sseResponse = try? Self.sharedDecoder.decode(SSEResponse.self, from: jsonData) {
            return convertSSEToImageGenerationResponse(sseResponse)
        }

        print("ImageGenerationResponseHandler: 无法解析图像生成响应: \(trimmedString)")
        return nil
    }

    private static func convertSSEToImageGenerationResponse(_ sseResponse: SSEResponse)
        -> Res<ImageGenerationResponse>?
    {
        let code = sseResponse.code
        let msg = extractMessage(from: sseResponse)

        guard let imageResponse = convertToImageGenerationResponse(sseResponse) else {
            return nil
        }

        return Res(code: code, msg: msg, data: imageResponse)
    }

    private static func convertToImageGenerationResponse(_ sseResponse: SSEResponse) -> ImageGenerationResponse? {
        let code = sseResponse.code

        switch sseResponse.type {
        case "status":
            return .status(ImageGenerationStatusResponse(
                code: code,
                type: sseResponse.type,
                event: sseResponse.event ?? "",
                node: sseResponse.node ?? ""
            ))

        case "tool_image_generation":
            return parseImageGenerationContent(sseResponse, code: code)

        default:
            return nil
        }
    }

    private static func parseImageGenerationContent(_ sseResponse: SSEResponse, code: Int) -> ImageGenerationResponse? {
        guard let contentDict = sseResponse.content?.value as? [String: Any] else {
            print("ImageGenerationResponseHandler: content解析失败")
            return nil
        }

        do {
            let content = try createImageGenerationContent(from: contentDict)

            return .imageGeneration(ImageGenerationContentResponse(
                code: code,
                type: sseResponse.type,
                content: content
            ))
        } catch {
            print("ImageGenerationResponseHandler: 内容解析失败: \(error)")
            return nil
        }
    }

    private static func createImageGenerationContent(from dict: [String: Any]) throws -> ImageGenerationContent {
        let status = dict["status"] as? String ?? ""
        let partialImageIndex = dict["partial_image_index"] as? Int
        let imageB64 = dict["image_b64"] as? String
        let text = dict["text"] as? String
        let imageUrl = dict["image_url"] as? String

        return ImageGenerationContent(
            status: status,
            partialImageIndex: partialImageIndex,
            imageB64: imageB64,
            text: text,
            imageUrl: imageUrl
        )
    }

    private static func extractMessage(from sseResponse: SSEResponse) -> String {
        if let stringContent = sseResponse.content?.value as? String {
            return stringContent
        }
        return ""
    }
}

extension ImageGenerationResponseHandler {
    static func batchParseResponses(_ jsonStrings: [String]) -> [Res<ImageGenerationResponse>] {
        return jsonStrings.compactMap { parseImageGenerationResponse(from: $0) }
    }

    static func isImageGenerationResponse(_ jsonString: String) -> Bool {
        guard let jsonData = jsonString.data(using: .utf8),
              let sseResponse = try? sharedDecoder.decode(SSEResponse.self, from: jsonData)
        else {
            return false
        }

        return sseResponse.type == "status" || sseResponse.type == "tool_image_generation"
    }
}
