import EventSource
import Foundation

// MARK: - SSE连接状态

enum SSEConnectionState: Equatable {
    case disconnected
    case connecting
    case connected
    case reconnecting
    case failed(Error)

    static func == (lhs: SSEConnectionState, rhs: SSEConnectionState) -> Bool {
        switch (lhs, rhs) {
        case (.connected, .connected),
             (.connecting, .connecting),
             (.disconnected, .disconnected),
             (.reconnecting, .reconnecting):
            return true
        case (.failed(let lhsError), .failed(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

// MARK: - SSE特定错误类型

enum SSEError: Error {
    case connectionFailed
    case parseError(String)
    case invalidResponse
    case maxRetriesExceeded
    case cancelled
    case networkUnavailable

    var localizedDescription: String {
        switch self {
        case .connectionFailed:
            return "SSE连接失败"
        case .parseError(let details):
            return "SSE数据解析错误: \(details)"
        case .invalidResponse:
            return "无效的SSE响应格式"
        case .maxRetriesExceeded:
            return "超过最大重试次数"
        case .cancelled:
            return "SSE连接已取消"
        case .networkUnavailable:
            return "网络不可用"
        }
    }
}

// MARK: - EventSource适配器主类

@MainActor
final class EventSourceAdapter: ObservableObject {
    private enum Config {
        static let maxRetryCount = 5
        static let baseRetryInterval: TimeInterval = 1.0
        static let maxRetryInterval: TimeInterval = 30.0
        static let connectionTimeout: TimeInterval = 600.0
    }

    @Published var connectionState: SSEConnectionState = .disconnected

    private var currentEventSource: EventSource?
    // SSE协议层：事件流管理
    private var currentDataTask: EventSource.DataTask?
    // 网络层：HTTP连接管理
    private var currentURLSession: URLSession?

    // MARK: - 连接状态

    private var retryCount = 0
    private var isManuallyDisconnected = false
    private let networkMonitor = NetworkMonitor.shared

    deinit {
        isManuallyDisconnected = true
        currentEventSource = nil
        currentDataTask = nil
        currentURLSession = nil
    }

    // MARK: - 公共方法

    /// 连接SSE端点并返回AsyncStream数据流
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - parameters: 请求参数
    ///   - headers: 自定义请求头
    /// - Returns: AsyncStream<Res<T>>数据流
    func connect<T: Decodable>(
        _ endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) -> AsyncStream<Res<T>> {
        return AsyncStream { continuation in
            Task { @MainActor in
                // 如果已有连接，先断开
                await disconnect()

                // 重置重试计数
                retryCount = 0
                isManuallyDisconnected = false

                // 开始连接（支持重连）
                await startConnectionWithRetry(
                    endpoint: endpoint,
                    parameters: parameters,
                    headers: headers,
                    continuation: continuation
                )
            }

            continuation.onTermination = { @Sendable _ in
                Task { @MainActor in
                    await self.disconnect()
                }
            }
        }
    }

    /// 连接SSE端点并返回原始JSON字符串流，用于优化的数据处理
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - parameters: 请求参数
    ///   - headers: 自定义请求头
    /// - Returns: AsyncStream<String>原始数据流
    func connectRaw(
        _ endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) -> AsyncStream<String> {
        return AsyncStream { continuation in
            Task { @MainActor in
                // 如果已有连接，先断开
                await disconnect()

                // 重置重试计数
                retryCount = 0
                isManuallyDisconnected = false

                // 开始连接（支持重连）
                await startRawConnectionWithRetry(
                    endpoint: endpoint,
                    parameters: parameters,
                    headers: headers,
                    continuation: continuation
                )
            }

            continuation.onTermination = { @Sendable _ in
                Task { @MainActor in
                    await self.disconnect()
                }
            }
        }
    }

    /// 断开SSE连接
    func disconnect() async {
        guard !isManuallyDisconnected else {
            return
        }

        isManuallyDisconnected = true

        // 取消当前的dataTask
        if let dataTask = currentDataTask {
            let urlSession = currentURLSession ?? URLSession.shared
            dataTask.cancel(urlSession: urlSession)
            currentDataTask = nil
        }

        cleanupSSEResources()
        connectionState = .disconnected

        print("\(DateUtils.formatTimeOnly()) ==> EventSource: SSE连接已断开")
    }

    /// 清理所有SSE连接相关资源
    private func cleanupSSEResources() {
        currentEventSource = nil
        currentDataTask = nil
        currentURLSession = nil
    }

    // 重试操作
    private func startConnectionWithRetry(
        endpoint: String,
        parameters: [String: Any]?,
        headers: [String: String]?,
        continuation: AsyncStream<Res<some Decodable>>.Continuation
    ) async {
        while retryCount <= Config.maxRetryCount, !isManuallyDisconnected {
            do {
                try await startConnection(
                    endpoint: endpoint,
                    parameters: parameters,
                    headers: headers,
                    continuation: continuation
                )
                return
            } catch {
                print("\(DateUtils.formatTimeOnly()) ==> EventSource: 连接失败: \(error)")
                connectionState = .failed(error)

                retryCount += 1

                if retryCount > Config.maxRetryCount {
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: 超过最大重试次数，停止重连")
                    connectionState = .failed(SSEError.maxRetriesExceeded)
                    continuation.finish()
                    return
                }

                // 计算重连延迟（指数退避）
                let delay = min(
                    Config.baseRetryInterval * pow(2.0, Double(retryCount - 1)),
                    Config.maxRetryInterval
                )

                print("\(DateUtils.formatTimeOnly()) ==> EventSource: 第\(retryCount)次重连，延迟\(delay)秒")
                connectionState = .reconnecting

                try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }
    }

    // 原始数据流重试操作
    private func startRawConnectionWithRetry(
        endpoint: String,
        parameters: [String: Any]?,
        headers: [String: String]?,
        continuation: AsyncStream<String>.Continuation
    ) async {
        while retryCount <= Config.maxRetryCount, !isManuallyDisconnected {
            do {
                try await startRawConnection(
                    endpoint: endpoint,
                    parameters: parameters,
                    headers: headers,
                    continuation: continuation
                )
                return
            } catch {
                print("\(DateUtils.formatTimeOnly()) ==> EventSource: 原始连接失败: \(error)")
                connectionState = .failed(error)

                retryCount += 1

                if retryCount > Config.maxRetryCount {
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: 超过最大重试次数，停止重连")
                    connectionState = .failed(SSEError.maxRetriesExceeded)
                    continuation.finish()
                    return
                }

                // 计算重连延迟（指数退避）
                let delay = min(
                    Config.baseRetryInterval * pow(2.0, Double(retryCount - 1)),
                    Config.maxRetryInterval
                )

                print("\(DateUtils.formatTimeOnly()) ==> EventSource: 第\(retryCount)次重连，延迟\(delay)秒")
                connectionState = .reconnecting

                try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }
    }

    // 建立 SSE 请求
    private func startConnection(
        endpoint: String,
        parameters: [String: Any]?,
        headers: [String: String]?,
        continuation: AsyncStream<Res<some Decodable>>.Continuation
    ) async throws {
        // 检查网络状态
        guard networkMonitor.isReachable else {
            let error = SSEError.networkUnavailable
            connectionState = .failed(error)
            throw error
        }

        connectionState = .connecting

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        print("\n\(DateUtils.formatTimeOnly()) ==> EventSource: SSE连接开始: \(url)")
        print("SSE请求参数: \(String(describing: parameters))")

        do {
            let urlRequest = try await createURLRequest(
                url: url,
                parameters: parameters,
                headers: headers
            )

            // 此处创建仅用于cancel操作，因为 EventSource 的 cancel 新增需要实例参数
            let sessionConfig = URLSessionConfiguration.default
            let urlSession = URLSession(configuration: sessionConfig)
            currentURLSession = urlSession

            let eventSource = EventSource()
            currentEventSource = eventSource

            // 开始连接
            let dataTask = eventSource.dataTask(for: urlRequest)
            currentDataTask = dataTask

            // 处理事件流
            try await processEventStream(dataTask: dataTask, continuation: continuation)

        } catch {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 连接创建失败: \(error)")
            connectionState = .failed(error)
            throw error
        }
    }

    // 建立原始数据流SSE请求
    private func startRawConnection(
        endpoint: String,
        parameters: [String: Any]?,
        headers: [String: String]?,
        continuation: AsyncStream<String>.Continuation
    ) async throws {
        // 检查网络状态
        guard networkMonitor.isReachable else {
            let error = SSEError.networkUnavailable
            connectionState = .failed(error)
            throw error
        }

        connectionState = .connecting

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        print("\n\(DateUtils.formatTimeOnly()) ==> EventSource: 原始SSE连接开始: \(url)")
        print("SSE请求参数: \(String(describing: parameters))")

        do {
            let urlRequest = try await createURLRequest(
                url: url,
                parameters: parameters,
                headers: headers
            )

            // 创建URLSession用于cancel操作
            let sessionConfig = URLSessionConfiguration.default
            let urlSession = URLSession(configuration: sessionConfig)
            currentURLSession = urlSession

            let eventSource = EventSource()
            currentEventSource = eventSource

            // 开始连接
            let dataTask = eventSource.dataTask(for: urlRequest)
            currentDataTask = dataTask

            // 处理原始事件流
            try await processRawEventStream(dataTask: dataTask, continuation: continuation)

        } catch {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 原始连接创建失败: \(error)")
            connectionState = .failed(error)
            throw error
        }
    }

    private func createURLRequest(
        url: String,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) async throws -> URLRequest {
        guard let requestURL = URL(string: url) else {
            throw SSEError.invalidResponse
        }

        var urlRequest = URLRequest(url: requestURL)
        urlRequest.httpMethod = "POST"
        urlRequest.timeoutInterval = Config.connectionTimeout

        // 设置基础请求头
        urlRequest.setValue("text/event-stream", forHTTPHeaderField: "Accept")
        urlRequest.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
        urlRequest.setValue(Language.deviceLanguage, forHTTPHeaderField: "locale")
        urlRequest.setValue(DateUtils.formatDateWithWeekday(), forHTTPHeaderField: "localtime")

        // 添加认证头
        if let authToken = await getAuthToken() {
            urlRequest.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        }

        // 设置自定义请求头
        if let headers {
            for (key, value) in headers {
                urlRequest.setValue(value, forHTTPHeaderField: key)
            }
        }

        // 设置请求体
        if let parameters {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters)
            urlRequest.httpBody = jsonData
            urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        }

        var logMessage = "\nSSE Request Headers:"
        if let allHeaders = urlRequest.allHTTPHeaderFields {
            for (key, value) in allHeaders.sorted(by: { $0.key < $1.key }) {
                // 隐藏敏感信息
                if key.lowercased() == "authorization" {
                    let maskedValue = value.count > 20 ? "\(value.prefix(10))...***" : "***"
                    logMessage += "\n\(key): \(maskedValue)"
                } else {
                    logMessage += "\n\(key): \(value)"
                }
            }
        }
        print(logMessage)

        return urlRequest
    }

    private func getAuthToken() async -> String? {
        return AuthStore.shared.getAccessToken()
    }

    // 处理stream流响应
    private func processEventStream(
        dataTask: EventSource.DataTask,
        continuation: AsyncStream<Res<some Decodable>>.Continuation
    ) async throws {
        defer {
            currentDataTask = nil
        }

        do {
            for await event in dataTask.events() {
                // 检查是否已手动断开连接
                if isManuallyDisconnected {
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: 检测到手动断开，停止处理事件")
                    break
                }

                switch event {
                case .open:
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: SSE连接已建立")
                    connectionState = .connected
                    retryCount = 0

                case .event(let eventData):
                    // 再次检查是否已断开，避免处理断开后的事件
                    if !isManuallyDisconnected {
                        handleEventData(eventData, continuation: continuation)
                    }

                case .error(let error):
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: SSE连接错误: \(error)")
                    connectionState = .failed(error)
                    throw error

                case .closed:
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: SSE连接关闭")
                    connectionState = .disconnected
                    continuation.finish()
                }
            }
        } catch {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 事件流处理错误: \(error)")
            connectionState = .failed(error)
            throw error
        }
    }

    // 处理原始数据流响应
    private func processRawEventStream(
        dataTask: EventSource.DataTask,
        continuation: AsyncStream<String>.Continuation
    ) async throws {
        defer {
            currentDataTask = nil
        }

        do {
            for await event in dataTask.events() {
                // 检查是否已手动断开连接
                if isManuallyDisconnected {
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: 检测到手动断开，停止处理原始事件")
                    break
                }

                switch event {
                case .open:
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: 原始SSE连接已建立")
                    connectionState = .connected
                    retryCount = 0

                case .event(let eventData):
                    // 再次检查是否已断开，避免处理断开后的事件
                    if !isManuallyDisconnected {
                        handleRawEventData(eventData, continuation: continuation)
                    }

                case .error(let error):
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: 原始SSE连接错误: \(error)")
                    connectionState = .failed(error)
                    throw error

                case .closed:
                    print("\(DateUtils.formatTimeOnly()) ==> EventSource: 原始SSE连接关闭")
                    connectionState = .disconnected
                    continuation.finish()
                }
            }
        } catch {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 原始事件流处理错误: \(error)")
            connectionState = .failed(error)
            throw error
        }
    }

    private func handleEventData(
        _ eventData: EVEvent,
        continuation: AsyncStream<Res<some Decodable>>.Continuation
    ) {
        guard let data = eventData.data else {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 收到空数据事件")
            return
        }

        // 检查是否为流结束标识符
        if isStreamEndMarker(data) {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 检测到流结束标识符")
            connectionState = .disconnected
            continuation.finish()
            return
        }

        // 解析JSON数据
        parseAndEmitData(jsonString: data, continuation: continuation)
    }

    private func handleRawEventData(
        _ eventData: EVEvent,
        continuation: AsyncStream<String>.Continuation
    ) {
        guard let data = eventData.data else {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 收到空原始数据事件")
            return
        }

        continuation.yield(data)

        if isStreamEndMarker(data) {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 检测到链结束事件，结束图像生成流")
            connectionState = .disconnected
            continuation.finish()
            return
        }
    }

    private func parseAndEmitData<T: Decodable>(
        jsonString: String,
        continuation: AsyncStream<Res<T>>.Continuation
    ) {
        let trimmedString = jsonString.trimmingCharacters(in: .whitespaces)

        // 跳过空数据
        guard !trimmedString.isEmpty else { return }

        guard let jsonData = trimmedString.data(using: .utf8) else {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 数据转换失败: \(trimmedString)")
            return
        }

        do {
            // 首先尝试解析为SSEResponse格式
            if let sseResponse = try? JSONDecoder().decode(SSEResponse.self, from: jsonData) {
                // 尝试转换为文本聊天响应
                if let chatResponse = convertSSEResponseToChatCompletion(sseResponse) as? T {
                    let wrappedResponse = Res(
                        code: sseResponse.code,
                        msg: sseResponse.content?.value as? String ?? "",
                        data: chatResponse
                    )
                    print(
                        "\(DateUtils.formatTimeOnly()) <== \(sseResponse.code) EventSource: data ok - type: \(sseResponse.type)"
                    )
                    continuation.yield(wrappedResponse)
                    return
                }

                // 如果文本聊天转换失败，尝试转换为图片生成响应
                if let imageResponse = convertSSEResponseToImageGeneration(sseResponse) as? T {
                    let wrappedResponse = Res(
                        code: sseResponse.code,
                        msg: sseResponse.content?.value as? String ?? "",
                        data: imageResponse
                    )
                    continuation.yield(wrappedResponse)
                    return
                }

                // 响应数据转换成符合图片识别类型的数据
                if let imageResponse = convertSSEResponseToImageRecognition(sseResponse) as? T {
                    let wrappedResponse = Res(
                        code: sseResponse.code,
                        msg: sseResponse.content?.value as? String ?? "",
                        data: imageResponse
                    )
                    continuation.yield(wrappedResponse)
                    return
                }
            }

            //  尝试直接解析为目标类型
            // if let directResponse = try? JSONDecoder().decode(T.self, from: jsonData) {
            //     let wrappedResponse = Res(code: directResponse., msg: "", data: directResponse)
            //     print("\(DateUtils.formatTimeOnly()) ==> EventSource: 直接解析成功")
            //     continuation.yield(wrappedResponse)
            //     return
            // }

            // 尝试解析为Res格式
            let response = try JSONDecoder().decode(Res<T>.self, from: jsonData)
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: Res格式解析成功")
            continuation.yield(response)

        } catch {
            print("\(DateUtils.formatTimeOnly()) ==> EventSource: 数据解析失败: \(error.localizedDescription)")
            print("原始数据: \(trimmedString)")
        }
    }

    /// 将SSE响应转换为图片生成响应
    private func convertSSEResponseToImageGeneration(_ sseResponse: SSEResponse) -> ImageGenerationResponse? {
        let code = sseResponse.code

        switch sseResponse.type {
        case "status":
            // 状态响应
            return .status(ImageGenerationStatusResponse(
                code: code,
                type: sseResponse.type,
                event: sseResponse.event ?? "",
                node: sseResponse.node ?? ""
            ))

        case "tool_image_generation":
            // 图片生成内容响应
            guard let contentDict = sseResponse.content?.value as? [String: Any] else {
                print("图片生成响应content解析失败")
                return nil
            }

            do {
                let contentData = try JSONSerialization.data(withJSONObject: contentDict)
                let content = try JSONDecoder().decode(ImageGenerationContent.self, from: contentData)

                return .imageGeneration(ImageGenerationContentResponse(
                    code: code,
                    type: sseResponse.type,
                    content: content
                ))
            } catch {
                print("图片生成内容解析失败: \(error)")
                return nil
            }

        default:
            // 未知类型，返回nil让其他解析器处理
            return nil
        }
    }

    private func isStreamEndMarker(_ data: String) -> Bool {
        let trimmed = data.trimmingCharacters(in: .whitespaces)

        guard let jsonData = trimmed.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any]
        else {
            return false
        }

        // 检查是否为链结束事件：{"type": "status", "event": "on_chain_end"}
        if let type = json["type"] as? String,
           let event = json["event"] as? String,
           type == "status", event == "on_chain_end"
        {
            return true
        }

        return false
    }
}

// MARK: - 便利扩展

extension EventSourceAdapter {
    static let shared = EventSourceAdapter()

    /// 连接状态的便利属性
    var isConnected: Bool {
        if case .connected = connectionState {
            return true
        }
        return false
    }

    var isConnecting: Bool {
        if case .connecting = connectionState {
            return true
        }
        return false
    }

    var isReconnecting: Bool {
        if case .reconnecting = connectionState {
            return true
        }
        return false
    }

    /// 验证连接是否真正断开
    var isFullyDisconnected: Bool {
        return isManuallyDisconnected &&
            currentEventSource == nil &&
            currentDataTask == nil &&
            currentURLSession == nil &&
            connectionState == .disconnected
    }

    /// 获取连接状态的调试信息
    var debugConnectionInfo: String {
        return """
        EventSourceAdapter状态:
        - connectionState: \(connectionState)
        - isManuallyDisconnected: \(isManuallyDisconnected)
        - currentEventSource: \(currentEventSource != nil ? "存在" : "nil")
        - currentDataTask: \(currentDataTask != nil ? "存在" : "nil")
        - currentURLSession: \(currentURLSession != nil ? "存在" : "nil")
        - isFullyDisconnected: \(isFullyDisconnected)
        """
    }
}
