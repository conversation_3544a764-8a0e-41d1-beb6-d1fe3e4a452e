import AVFoundation
import Combine
import Foundation
import MicrosoftCognitiveServicesSpeech
import UIKit

// MARK: - Azure Speech SDK 服务状态

enum AzureSpeechState: Equatable, Hashable {
    case idle
    case listening // STT状态
    case processing // STT状态
    case waitingForRetry // STT状态：等待重试（短录音或空结果后）
    case preparing // TTS状态：准备播放（包括缓存加载）
    case preparingCached // TTS状态：准备缓存音频
    case synthesizing // TTS状态：正在合成
    case playing // TTS状态：正在播放
    case paused // TTS状态：播放暂停
    case error(String)

    // MARK: - Hashable实现

    func hash(into hasher: inout Hasher) {
        switch self {
        case .idle:
            hasher.combine("idle")
        case .listening:
            hasher.combine("listening")
        case .processing:
            hasher.combine("processing")
        case .waitingForRetry:
            hasher.combine("waitingForRetry")
        case .preparing:
            hasher.combine("preparing")
        case .preparingCached:
            hasher.combine("preparingCached")
        case .synthesizing:
            hasher.combine("synthesizing")
        case .playing:
            hasher.combine("playing")
        case .paused:
            hasher.combine("paused")
        case .error(let message):
            hasher.combine("error")
            hasher.combine(message)
        }
    }
}

// MARK: - TTS UI显示状态枚举

enum TTSDisplayState: Equatable {
    case idle
    case preparing
    case preparingCached
    case synthesizing(progress: Float)
    case playing
    case paused
    case error(String)

    var isActive: Bool {
        switch self {
        case .idle:
            return false
        default:
            return true
        }
    }

    var displayText: String {
        switch self {
        case .idle:
            return "待机"
        case .preparing:
            return "准备中"
        case .preparingCached:
            return "加载缓存"
        case .synthesizing(let progress):
            return "合成中 \(Int(progress * 100))%"
        case .playing:
            return "播放中"
        case .paused:
            return "已暂停"
        case .error(let message):
            return "错误: \(message)"
        }
    }

    var showProgress: Bool {
        switch self {
        case .preparingCached, .synthesizing:
            return true
        default:
            return false
        }
    }
}

// MARK: - 音频会话模式

enum AudioSessionMode {
    case recording // STT录音模式
    case playback // TTS播放模式
    case idle // 空闲模式
}

// MARK: - Azure Speech SDK 服务

@MainActor
class AzureSpeechSDKService: NSObject, ObservableObject {
    // MARK: - STT相关属性

    @Published var state: AzureSpeechState = .idle
    @Published var recognizedText = ""
    @Published var partialResult = ""
    @Published var isListening = false
    @Published var hasPermission = false
    private var wasManuallyStopped = false
    private var isManualConfirmation = false

    // MARK: - TTS相关属性

    @Published var isSynthesizing = false
    @Published var isPlaying = false
    @Published var currentPlayingText = ""
    @Published var synthesisProgress: Float = 0.0
    @Published var selectedVoice: SpeechVoice = .xiaoxiaoMultilingual

    var ttsDisplayState: TTSDisplayState {
        switch state {
        case .preparing:
            return .preparing
        case .preparingCached:
            return .preparingCached
        case .synthesizing:
            return .synthesizing(progress: synthesisProgress)
        case .playing:
            return .playing
        case .paused:
            return .paused
        case .error(let message):
            return .error(message)
        default:
            return .idle
        }
    }

    // MARK: - Azure Speech SDK 组件

    var speechConfig: SPXSpeechConfiguration?
    private var audioConfig: SPXAudioConfiguration?
    private var speechRecognizer: SPXSpeechRecognizer?
    private var speechSynthesizer: SPXSpeechSynthesizer?

    // MARK: - TTS音频播放组件

    private var audioPlayer: AVAudioPlayer?
    private var synthesisResult: SPXSpeechSynthesisResult?
    private var currentAudioSessionMode: AudioSessionMode = .idle

    // MARK: - 合成器池管理

    private var synthesizerPool: [SPXSpeechSynthesizer] = []
    private let maxSynthesizerPoolSize = 2

    // MARK: - 事件处理

    private var cancellables = Set<AnyCancellable>()

    // MARK: - 录音时长追踪

    private var recordingStartTime: Date?

    // MARK: - TTS缓存管理

    private let ttsCache = TTSCache()

    private var serviceRegion: String = AzureSpeechConfig.serviceRegion
    private let recognitionLanguage = AzureSpeechConfig.defaultRecognitionLanguage

    private let audioTokenService = AudioTokenService.shared

    /// 获取JWT token
    private func getJWTToken() async throws -> (token: String, region: String) {
        let jwtToken = try await audioTokenService.getAzureToken()
        let regionToUse = AzureSpeechConfig.serviceRegion
        return (jwtToken, regionToUse)
    }

    override init() {
        super.init()

        setupBindings()
        Task {
            await checkPermissions()
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
        synthesizerPool.removeAll()
    }

    // MARK: - 合成器池管理

    private func getOrCreateSynthesizer(config: SPXSpeechConfiguration) throws -> SPXSpeechSynthesizer {
        if let synthesizer = synthesizerPool.popLast() {
            return synthesizer
        }
        return try SPXSpeechSynthesizer(speechConfiguration: config, audioConfiguration: nil)
    }

    private func returnSynthesizerToPool(_ synthesizer: SPXSpeechSynthesizer) {
        guard synthesizerPool.count < maxSynthesizerPoolSize else { return }
        synthesizerPool.append(synthesizer)
    }

    private func setupBindings() {
        $state
            .map { state in
                switch state {
                case .listening:
                    return true
                default:
                    return false
                }
            }
            .removeDuplicates()
            .assign(to: \.isListening, on: self)
            .store(in: &cancellables)

        $state
            .map { state in
                switch state {
                case .preparing, .preparingCached, .synthesizing:
                    return true
                default:
                    return false
                }
            }
            .removeDuplicates()
            .assign(to: \.isSynthesizing, on: self)
            .store(in: &cancellables)

        $state
            .map { state in
                switch state {
                case .playing:
                    return true
                default:
                    return false
                }
            }
            .removeDuplicates()
            .assign(to: \.isPlaying, on: self)
            .store(in: &cancellables)
    }

    @objc private func audioRouteChanged(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue)
        else {
            return
        }

        switch reason {
        case .oldDeviceUnavailable:
            if isPlaying {
                Task {
                    await togglePlayback()
                }
            }

        default:
            break
        }
    }

    @objc private func audioSessionInterrupted(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue)
        else {
            return
        }

        switch type {
        case .began:
            if isPlaying {
                Task {
                    await stopPlayback()
                }
            }

        case .ended:
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {}
            }

        @unknown default:
            break
        }
    }

    // MARK: - 初始化 Azure SDK

    private func initializeAzureSDK(with jwtToken: String, region: String) {
        do {
            speechConfig = try SPXSpeechConfiguration(authorizationToken: jwtToken, region: region)
            speechConfig?.speechRecognitionLanguage = recognitionLanguage
            speechConfig?.outputFormat = SPXOutputFormat.detailed
            audioConfig = SPXAudioConfiguration()
        } catch {
            print("❌ Azure SDK初始化失败: \(error.localizedDescription)")
            state = .error("Azure SDK 初始化失败: \(error.localizedDescription)")
        }
    }

    func initializeWithJWTToken() async throws {
        let tokenInfo = try await getJWTToken()

        await MainActor.run {
            serviceRegion = tokenInfo.region
            initializeAzureSDK(with: tokenInfo.token, region: tokenInfo.region)
        }
    }

    // MARK: - 权限管理

    func checkPermissions() async {
        let microphoneAuthStatus = AVAudioSession.sharedInstance().recordPermission

        await MainActor.run {
            let microphoneGranted = switch microphoneAuthStatus {
            case .granted:
                true

            case .denied:
                false

            case .undetermined:
                false

            @unknown default:
                false
            }

            self.hasPermission = microphoneGranted
        }
    }

    /// 确保权限已授权，如果未授权则请求权限
    func ensurePermissions() async -> Bool {
        await checkPermissions()

        if !hasPermission {
            return await requestPermissions()
        }

        return true
    }

    func requestPermissions() async -> Bool {
        _ = AVAudioSession.sharedInstance().recordPermission

        // 申请麦克风权限
        let microphoneAuthStatus = await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }

        await MainActor.run {
            self.hasPermission = microphoneAuthStatus

            if !microphoneAuthStatus {
                let errorMessage = "权限不足：麦克风权限被拒绝\n请在设置中允许麦克风访问"
                self.state = .error(errorMessage)
            }
        }

        return microphoneAuthStatus
    }

    // MARK: - 语音识别 (SDK)

    func startContinuousRecognition() async throws {
        guard await ensurePermissions() else {
            throw AzureSpeechSDKError.permissionDenied
        }

        try await initializeWithJWTToken()

        guard let speechConfig,
              let audioConfig
        else {
            throw AzureSpeechSDKError.configurationError
        }

        await stopContinuousRecognition()

        do {
            try await configureAudioSession()

            // 创建语音识别器
            speechRecognizer = try SPXSpeechRecognizer(
                speechConfiguration: speechConfig,
                audioConfiguration: audioConfig
            )

            // 设置事件处理器
            setupSpeechRecognizerEvents()

            // 开始连续识别
            try speechRecognizer?.startContinuousRecognition()

            // 更新状态
            state = .listening
            recognizedText = ""
            partialResult = ""

            // 记录录音开始时间
            recordingStartTime = Date()
            wasManuallyStopped = false
            isManualConfirmation = false

        } catch {
            print("❌ STT启动失败: \(error.localizedDescription)")
            state = .error("启动识别失败: \(error.localizedDescription)")
            throw AzureSpeechSDKError.recognitionStartFailed(error.localizedDescription)
        }
    }

    func stopContinuousRecognition(isManualConfirmation: Bool = false) async {
        self.isManualConfirmation = isManualConfirmation
        do {
            if state == .listening {
                state = .processing
            }

            try speechRecognizer?.stopContinuousRecognition()

            if state == .processing {
                state = .idle
            }

        } catch {
            state = .idle
        }
        speechRecognizer = nil
    }

    /// 停止语音识别
    func forceStopRecognition() async {
        wasManuallyStopped = true
        await stopContinuousRecognition()

        state = .idle
    }

    // MARK: - 设置 Speech Recognizer 事件处理器

    private func setupSpeechRecognizerEvents() {
        guard let recognizer = speechRecognizer else { return }

        // 识别中事件
        recognizer.addRecognizingEventHandler { [weak self] _, event in
            guard let self else { return }
            let result = event.result

            Task { @MainActor in
                self.partialResult = result.text ?? ""
            }
        }

        // 识别完成事件
        recognizer.addRecognizedEventHandler { [weak self] _, event in
            guard let self else { return }
            let result = event.result

            Task { @MainActor in
                let finalText = result.text ?? ""

                if self.wasManuallyStopped {
                    self.state = .idle
                    return
                }

                let recordingDuration = self.getRecordingDuration()

                if !self.wasManuallyStopped, !self.isManualConfirmation {
                    if recordingDuration < 2.0, finalText.isEmpty {
                        self.showToastForShortRecording()
                        self.state = .waitingForRetry
                        return
                    } else if !finalText.isEmpty, recordingDuration < 1.0 {
                        self.showToastForShortRecording()
                        self.state = .waitingForRetry
                        return
                    } else if finalText.isEmpty, recordingDuration >= 2.0 {
                        self.showToastForEmptyResult()
                        self.state = .waitingForRetry
                        return
                    }
                }

                if !finalText.isEmpty, result.reason == SPXResultReason.recognizedSpeech {
                    if self.state == .listening || self.state == .processing {
                        self.recognizedText = finalText
                        self.partialResult = ""
                    }
                }
            }
        }

        // 会话开始事件
        recognizer.addSessionStartedEventHandler { [weak self] _, _ in
            guard let _ = self else { return }
        }

        // 会话停止事件
        recognizer.addSessionStoppedEventHandler { [weak self] _, _ in
            guard let self else { return }

            Task { @MainActor in
                if self.state == .listening {
                    self.state = .idle
                }
            }
        }

        // 取消/错误事件
        recognizer.addCanceledEventHandler { [weak self] _, event in
            guard let self else { return }

            Task { @MainActor in
                let errorMessage = event.errorDetails ?? "未知错误"
                self.state = .error("识别被取消: \(errorMessage)")
            }
        }
    }

    // MARK: - 语音合成 (TTS)

    func synthesizeSpeech(_ text: String, voice: SpeechVoice = .xiaoxiaoMultilingual) async throws {
        guard let speechConfig else {
            throw AzureSpeechSDKError.configurationError
        }

        do {
            // 设置语音
            speechConfig.speechSynthesisVoiceName = voice.rawValue

            // 创建语音合成器
            speechSynthesizer = try SPXSpeechSynthesizer(speechConfiguration: speechConfig, audioConfiguration: nil)

            guard let synthesizer = speechSynthesizer else {
                throw AzureSpeechSDKError.configurationError
            }

            let result = try synthesizer.speakText(text)

            // 检查结果
            if result.reason == SPXResultReason.canceled {
                throw AzureSpeechSDKError.synthesisError("语音合成被取消")
            }

        } catch {
            throw error
        }
    }

    // MARK: - 音频会话配置

    private func configureAudioSession() async throws {
        try await configureAudioSessionForMode(.recording)
    }

    /// 配置音频会话
    private func configureAudioSessionForMode(_ mode: AudioSessionMode) async throws {
        let audioSession = AVAudioSession.sharedInstance()

        guard currentAudioSessionMode != mode else {
            return
        }

        currentAudioSessionMode = mode

        do {
            switch mode {
            case .recording:
                try audioSession.setCategory(
                    .playAndRecord,
                    mode: .measurement,
                    options: [
                        .defaultToSpeaker,
                        .allowBluetooth,
                        .allowBluetoothA2DP,
                        .allowAirPlay,
                    ]
                )

                // 设置录音采样率和质量
                try audioSession.setPreferredSampleRate(44100.0)
                try audioSession.setPreferredIOBufferDuration(0.005)
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)

            case .playback:
                let availableRoutes = audioSession.availableInputs
                let hasBluetoothDevice = availableRoutes?.contains { route in
                    route.portType == .bluetoothHFP || route.portType == .bluetoothA2DP
                } ?? false

                var options: AVAudioSession.CategoryOptions = [.defaultToSpeaker, .allowAirPlay]

                // 如果有蓝牙设备，允许蓝牙播放
                if hasBluetoothDevice {
                    options.insert(.allowBluetooth)
                    options.insert(.allowBluetoothA2DP)
                }

                try audioSession.setCategory(
                    .playback,
                    mode: .spokenAudio,
                    options: options
                )

                try audioSession.setPreferredSampleRate(44100.0)
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)

            case .idle:
                try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            }

        } catch {
            try await fallbackAudioConfiguration(for: mode)
        }
    }

    /// 后备音频配置方案
    private func fallbackAudioConfiguration(for mode: AudioSessionMode) async throws {
        let audioSession = AVAudioSession.sharedInstance()

        switch mode {
        case .recording:
            try audioSession.setCategory(.playAndRecord)
            try audioSession.setActive(true)

        case .playback:
            try audioSession.setCategory(.playback)
            try audioSession.setActive(true)

        case .idle:
            try audioSession.setActive(false)
        }

        currentAudioSessionMode = mode
    }

    // MARK: - 公开 API

    func toggleRecording() async {
        switch state {
        case .idle, .waitingForRetry:
            do {
                try await startContinuousRecognition()
            } catch {
                await MainActor.run {
                    self.state = .error("无法开始录音: \(error.localizedDescription)")
                }
            }
        case .listening:
            await stopContinuousRecognition()
        default:
            break
        }
    }

    func getRecognizedTextAndClear() -> String {
        let text = recognizedText
        recognizedText = ""
        partialResult = ""
        recordingStartTime = nil
        return text
    }

    // MARK: - 录音时长计算

    private func getRecordingDuration() -> TimeInterval {
        guard let startTime = recordingStartTime else { return 0 }
        return Date().timeIntervalSince(startTime)
    }

    // MARK: - Toast提示方法

    private func showToastForShortRecording() {
        NotificationCenter.default.post(
            name: .speechRecognitionFeedback,
            object: nil,
            userInfo: ["type": "shortRecording", "message": "录音时间过短，请长按说话"]
        )
    }

    private func showToastForEmptyResult() {
        // 发送通知显示无识别结果提示
        NotificationCenter.default.post(
            name: .speechRecognitionFeedback,
            object: nil,
            userInfo: ["type": "emptyResult", "message": "未识别到语音内容，请重试"]
        )
    }

    // MARK: - 语音设置

    // 更新识别语言为: \(language.displayName)
    func updateRecognitionLanguage(_ language: SpeechRecognitionLanguage) {
        speechConfig?.speechRecognitionLanguage = language.rawValue
    }

    // 更新合成语音为: \(voice.displayName)
    func updateSynthesisVoice(_ voice: SpeechVoice) {
        speechConfig?.speechSynthesisVoiceName = voice.rawValue
        selectedVoice = voice
    }
}

// MARK: - TTS功能扩展

extension AzureSpeechSDKService {
    /// 合成并播放语音
    func synthesizeAndPlay(_ text: String, voice: SpeechVoice? = nil) async throws {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw AzureSpeechSDKError.synthesisError("文本内容为空")
        }

        // 只在需要时初始化SDK
        if speechConfig == nil {
            try await initializeWithJWTToken()
        }

        let cleanText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        let voiceToUse = voice ?? selectedVoice

        // 停止当前播放
        await stopPlayback()

        await updateStateToPreparingWithText(cleanText)

        do {
            let audioData: Data

            // 检查缓存
            if let cachedData = ttsCache.getCachedAudio(forText: cleanText, voice: voiceToUse) {
                audioData = cachedData

                // 设置缓存准备状态
                await updateStateToCachedPreparing(cleanText)

                await simulatePreparationForCachedAudioAdaptive(textLength: cleanText.count)
            } else {
                await updateStateToSynthesizing()
                audioData = try await performSynthesis(cleanText, voice: voiceToUse)

                ttsCache.cacheAudio(audioData, forText: cleanText, voice: voiceToUse)
            }

            // 播放音频
            try await playAudioData(audioData)

        } catch {
            print("❌ TTS合成失败: \(error.localizedDescription)")
            await handleSynthesisError(error)
            throw error
        }
    }

    // MARK: - 私有辅助方法

    private func updateStateToPreparingWithText(_ text: String) async {
        await MainActor.run {
            state = .preparing
            currentPlayingText = text
            synthesisProgress = 0.0
        }

        try? await Task.sleep(nanoseconds: 300_000_000)
    }

    /// 更新到合成状态
    private func updateStateToSynthesizing() async {
        await MainActor.run {
            state = .synthesizing
            synthesisProgress = 0.0
        }
    }

    /// 统一的缓存准备状态设置
    private func updateStateToCachedPreparing(_ text: String) async {
        await MainActor.run {
            state = .preparingCached
            currentPlayingText = text
            synthesisProgress = 0.0
        }
    }

    /// 为缓存音频模拟准备过程
    private func simulatePreparationForCachedAudio() async {
        let totalDuration: TimeInterval = 0.8
        let progressSteps = 8
        let stepDuration = totalDuration / Double(progressSteps)

        for i in 1...4 {
            let progress = Float(i) * 0.125
            await MainActor.run {
                synthesisProgress = progress
            }

            try? await Task.sleep(nanoseconds: UInt64(stepDuration * 0.6 * 1_000_000_000))
        }

        for i in 5...6 {
            let progress = Float(i) * 0.125
            await MainActor.run {
                synthesisProgress = progress
            }

            try? await Task.sleep(nanoseconds: UInt64(stepDuration * 0.8 * 1_000_000_000))
        }

        for i in 7...8 {
            let progress = Float(i) * 0.125
            await MainActor.run {
                synthesisProgress = progress
            }

            try? await Task.sleep(nanoseconds: UInt64(stepDuration * 1.2 * 1_000_000_000))
        }

        await MainActor.run {
            synthesisProgress = 1.0
        }
    }

    private func simulatePreparationForCachedAudioFast() async {
        let totalDuration: TimeInterval = 0.6
        let progressSteps = 4
        let stepDuration = totalDuration / Double(progressSteps)

        for i in 1...progressSteps {
            let progress = Float(i) * 0.25
            await MainActor.run {
                synthesisProgress = progress
            }
            try? await Task.sleep(nanoseconds: UInt64(stepDuration * 1_000_000_000))
        }
    }

    private func simulatePreparationForCachedAudioAdaptive(textLength: Int) async {
        if textLength < 20 {
            await simulatePreparationForCachedAudioFast()
        } else {
            await simulatePreparationForCachedAudio()
        }
    }

    private func performSynthesis(_ text: String, voice: SpeechVoice) async throws -> Data {
        if text.count > 50 {
            return try await performBackgroundSynthesis(text, voice: voice)
        } else {
            return try await synthesizeSpeechToData(text, voice: voice)
        }
    }

    private func performBackgroundSynthesis(_ text: String, voice: SpeechVoice) async throws -> Data {
        return try await withThrowingTaskGroup(of: Data.self) { group in
            group.addTask {
                try await self.synthesizeSpeechToData(text, voice: voice)
            }

            let progressTask = Task {
                await self.updateSynthesisProgress()
            }

            defer { progressTask.cancel() }

            guard let result = try await group.next() else {
                throw AzureSpeechSDKError.synthesisError("合成任务失败")
            }

            group.cancelAll()
            return result
        }
    }

    private func updateSynthesisProgress() async {
        do {
            for progress in stride(from: 0.1, through: 0.9, by: 0.1) {
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

                await MainActor.run {
                    self.synthesisProgress = Float(progress)
                }

                try Task.checkCancellation()
            }
        } catch {}
    }

    /// 统一的错误处理
    private func handleSynthesisError(_ error: Error) async {
        await MainActor.run {
            let errorMessage = "语音合成失败: \(error.localizedDescription)"
            self.state = .error(errorMessage)
            self.currentPlayingText = ""
            self.synthesisProgress = 0.0
        }

        if isNetworkError(error) {}
    }

    /// 判断是否为网络相关错误
    private func isNetworkError(_ error: Error) -> Bool {
        let errorString = error.localizedDescription.lowercased()
        return errorString.contains("network") ||
            errorString.contains("internet") ||
            errorString.contains("connection") ||
            errorString.contains("timeout") ||
            errorString.contains("ssl") ||
            errorString.contains("tls")
    }

    /// 合成语音到数据（带重试机制）
    private func synthesizeSpeechToData(_ text: String, voice: SpeechVoice) async throws -> Data {
        guard let speechConfig else {
            throw AzureSpeechSDKError.configurationError
        }

        let maxRetries = 3
        let baseDelay: TimeInterval = 1.0

        for attempt in 1...maxRetries {
            do {
                // 设置语音
                speechConfig.speechSynthesisVoiceName = voice.rawValue

                // 为长文本设置更长的超时时间
                if text.count > 100 {
                    // 注意：长文本检测，将依赖重试机制处理超时
                }

                // 获取或创建语音合成器（输出到内存）
                let synthesizer = try getOrCreateSynthesizer(config: speechConfig)

                // 执行合成
                let result = try synthesizer.speakText(text)

                // 将合成器返回到池中进行复用
                returnSynthesizerToPool(synthesizer)

                // 检查结果
                guard result.reason == SPXResultReason.synthesizingAudioCompleted else {
                    let errorMessage = getDetailedErrorMessage(from: result)

                    // 如果是网络相关错误且还有重试次数，继续重试
                    if attempt < maxRetries, isRetryableError(result.reason) {
                        let delay = baseDelay * Double(attempt)
                        try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                        continue
                    }

                    throw AzureSpeechSDKError.synthesisError(errorMessage)
                }

                guard let audioData = result.audioData, !audioData.isEmpty else {
                    throw AzureSpeechSDKError.synthesisError("合成的音频数据为空")
                }

                return audioData

            } catch {
                // 如果是最后一次尝试或者不是可重试的错误，抛出异常
                if attempt == maxRetries || !isRetryableNetworkError(error) {
                    throw error
                }

                // 等待后重试
                let delay = baseDelay * Double(attempt)
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }

        throw AzureSpeechSDKError.synthesisError("合成失败：已达到最大重试次数")
    }

    /// 获取详细的错误信息
    private func getDetailedErrorMessage(from result: SPXSpeechSynthesisResult) -> String {
        switch result.reason {
        case .canceled:
            return "语音合成被取消 - 可能是网络问题或请求超时"
        default:
            return "语音合成失败: \(result.reason.rawValue)"
        }
    }

    /// 判断是否为可重试的错误
    private func isRetryableError(_ reason: SPXResultReason) -> Bool {
        return reason == .canceled
    }

    /// 判断是否为可重试的网络错误
    private func isRetryableNetworkError(_ error: Error) -> Bool {
        let errorString = error.localizedDescription.lowercased()
        return errorString.contains("network") ||
            errorString.contains("timeout") ||
            errorString.contains("connection") ||
            errorString.contains("ssl") ||
            errorString.contains("tls")
    }

    /// 播放音频数据
    private func playAudioData(_ audioData: Data) async throws {
        try await configureAudioSessionForMode(.playback)

        audioPlayer = try AVAudioPlayer(data: audioData)
        audioPlayer?.delegate = self
        audioPlayer?.prepareToPlay()

        await MainActor.run {
            state = .playing
            synthesisProgress = 1.0
        }

        // 开始播放
        guard audioPlayer?.play() == true else {
            throw AzureSpeechSDKError.synthesisError("音频播放启动失败")
        }
    }

    /// 停止播放
    func stopPlayback() async {
        audioPlayer?.stop()
        audioPlayer = nil

        await MainActor.run {
            state = .idle
            currentPlayingText = ""
            synthesisProgress = 0.0
        }

        try? await configureAudioSessionForMode(.idle)
    }

    /// 暂停/恢复播放
    func togglePlayback() async {
        guard let player = audioPlayer else {
            return
        }

        await MainActor.run {
            if player.isPlaying {
                player.pause()
                state = .paused
            } else {
                player.play()
                state = .playing
            }
        }
    }

    /// 检查是否正在播放指定文本
    func isPlayingText(_ text: String) -> Bool {
        return isPlaying && currentPlayingText == text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// MARK: - AVAudioPlayerDelegate

extension AzureSpeechSDKService: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            self.state = .idle
            self.currentPlayingText = ""
            self.synthesisProgress = 0.0

            // 恢复音频会话到空闲状态
            try? await self.configureAudioSessionForMode(.idle)
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            let errorMessage = error?.localizedDescription ?? "未知音频解码错误"
            self.state = .error("音频播放错误: \(errorMessage)")
            self.currentPlayingText = ""

            // 恢复音频会话到空闲状态
            try? await self.configureAudioSessionForMode(.idle)
        }
    }
}

// MARK: - TTS缓存管理

class TTSCache {
    private let cache = NSCache<NSString, NSData>()
    private let maxCacheSize = 50 * 1024 * 1024 // 50MB

    // 性能统计
    private(set) var hitCount = 0
    private(set) var missCount = 0

    var currentCount: Int {
        return cache.countLimit
    }

    var hitRate: Double {
        let total = hitCount + missCount
        return total > 0 ? Double(hitCount) / Double(total) : 0.0
    }

    init() {
        cache.totalCostLimit = maxCacheSize
        cache.countLimit = 100 // 最多缓存100个音频文件

        // 监听内存警告
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.clearCache()
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    func cacheAudio(_ data: Data, forText text: String, voice: SpeechVoice) {
        let key = cacheKey(forText: text, voice: voice)
        cache.setObject(data as NSData, forKey: key, cost: data.count)
    }

    func getCachedAudio(forText text: String, voice: SpeechVoice) -> Data? {
        let key = cacheKey(forText: text, voice: voice)
        let result = cache.object(forKey: key) as Data?

        if result != nil {
            hitCount += 1
        } else {
            missCount += 1
        }

        return result
    }

    func clearCache() {
        cache.removeAllObjects()
        hitCount = 0
        missCount = 0
    }

    private func cacheKey(forText text: String, voice: SpeechVoice) -> NSString {
        return "\(text.hashValue)_\(voice.rawValue)" as NSString
    }

    /// 获取缓存统计信息
    func getCacheStats() -> (hitCount: Int, missCount: Int, hitRate: Double, currentCount: Int) {
        return (hitCount: hitCount, missCount: missCount, hitRate: hitRate, currentCount: currentCount)
    }
}

// MARK: - Azure Speech SDK 错误类型

enum AzureSpeechSDKError: LocalizedError {
    case permissionDenied
    case configurationError
    case recognitionStartFailed(String)
    case synthesisError(String)
    case audioSessionError(String)

    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "麦克风权限被拒绝"
        case .configurationError:
            return "Azure SDK 配置错误"
        case .recognitionStartFailed(let message):
            return "启动识别失败: \(message)"
        case .synthesisError(let message):
            return "语音合成错误: \(message)"
        case .audioSessionError(let message):
            return "音频会话错误: \(message)"
        }
    }
}
