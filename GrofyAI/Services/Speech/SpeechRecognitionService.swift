import AVFoundation
import Combine
import Foundation
import MicrosoftCognitiveServicesSpeech

// MARK: - STT状态枚举

enum STTState: Equatable {
    case idle // 空闲状态
    case requestingToken // 正在请求token
    case initializing // 正在初始化SDK
    case ready // 准备就绪，可以开始录音
    case listening // 正在录音
    case processing // 正在处理（停止录音但还在等待结果）
    case error(String) // 错误状态

    var isActive: Bool {
        switch self {
        case .error, .idle:
            return false
        default:
            return true
        }
    }

    var displayText: String {
        switch self {
        case .idle:
            return ""
        case .initializing, .requestingToken:
            return "正在准备..."
        case .ready:
            return "请开始说话"
        case .listening:
            return "正在聆听..."
        case .processing:
            return "正在处理..."
        case .error(let message):
            return message
        }
    }
}

// MARK: - 语音识别服务

/// 专门用于语音识别的服务
@MainActor
final class SpeechRecognitionService: ObservableObject {
    private let connectionPool = SpeechSDKConnectionPool()
    private let audioSessionManager = AudioSessionManager()

    // 识别器复用
    private var recognizer: SPXSpeechRecognizer?
    private var isRecognizerConfigured = false

    // 状态管理
    @Published var state: STTState = .idle
    @Published var recognizedText = ""
    @Published var partialText = ""

    // 录音计时
    @Published var recordingDuration: TimeInterval = 0
    private var recordingTimer: Timer?

    // 完成回调管理
    private var stopCompletionHandler: (() -> Void)?
    private var hasPendingFinalResult = false

    // 计算属性保持向后兼容
    var isListening: Bool {
        state == .listening
    }

    /// 开始连续识别（优化版本）
    func startContinuousRecognition() async throws {
        print("[STT] Starting continuous recognition...")

        // 重置状态
        state = .requestingToken
        recognizedText = ""
        partialText = ""
        recordingDuration = 0

        // 检查权限
        guard await checkMicrophonePermission() else {
            print("[STT] ❌ Microphone permission denied")
            state = .error("需要麦克风权限")
            throw AzureSpeechSDKError.permissionDenied
        }
        print("[STT] ✅ Microphone permission granted")

        // 配置音频会话
        do {
            try await audioSessionManager.configureForMode(.recording)
            print("[STT] ✅ Audio session configured for recording")
        } catch {
            print("[STT] ❌ Failed to configure audio session: \(error)")
            state = .error("音频配置失败")
            throw error
        }

        // 更新状态为初始化中
        state = .initializing

        // 获取或创建识别器
        let recognizer = try await getOrCreateRecognizer()
        print("[STT] ✅ Speech recognizer created")

        // 开始识别
        do {
            try recognizer.startContinuousRecognition()
            print("[STT] ✅ Started continuous recognition")
            state = .listening
            startRecordingTimer()
        } catch {
            print("[STT] ❌ Failed to start recognition: \(error)")
            state = .error("启动识别失败")
            throw error
        }
    }

    /// 停止识别（支持完成回调）
    func stopRecognition(completion: (() -> Void)? = nil) async {
        guard let recognizer, state == .listening else {
            print("[STT] No active recognition to stop")
            completion?()
            return
        }

        print("[STT] Stopping recognition...")

        // 更新状态为处理中
        state = .processing
        stopRecordingTimer()

        // 保存完成回调
        stopCompletionHandler = completion

        // 如果有待处理的最终结果，等待一小段时间
        if hasPendingFinalResult {
            print("[STT] Waiting for pending final result...")
            // 使用较短的超时时间，避免无限等待
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        }

        // 在后台线程执行停止操作
        await Task.detached(priority: .userInitiated) {
            do {
                try recognizer.stopContinuousRecognition()
                print("[STT] ✅ Recognition stopped successfully")
            } catch {
                print("[STT] ❌ Failed to stop recognition: \(error)")
            }

            // 更新状态并调用回调
            await MainActor.run {
                self.state = .idle
                self.stopCompletionHandler?()
                self.stopCompletionHandler = nil
            }
        }.value

        // 延迟停用音频会话
        audioSessionManager.deactivateAfterDelay()
    }

    /// 获取并清除识别结果
    func getRecognizedTextAndClear() -> String {
        // 优先使用最终识别结果，如果没有则使用部分结果
        let text = recognizedText.isEmpty ? partialText : recognizedText
        recognizedText = ""
        partialText = ""
        return text
    }

    // MARK: - 录音计时器管理

    private func startRecordingTimer() {
        stopRecordingTimer()
        recordingDuration = 0

        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            Task { @MainActor in
                self.recordingDuration += 1.0
            }
        }
    }

    private func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
    }

    // MARK: - 私有方法

    private func checkMicrophonePermission() async -> Bool {
        switch AVAudioSession.sharedInstance().recordPermission {
        case .granted:
            return true
        case .denied:
            return false
        case .undetermined:
            return await withCheckedContinuation { continuation in
                AVAudioSession.sharedInstance().requestRecordPermission { granted in
                    continuation.resume(returning: granted)
                }
            }
        @unknown default:
            return false
        }
    }

    private func getOrCreateRecognizer() async throws -> SPXSpeechRecognizer {
        // 如果已有识别器且配置有效，直接返回
        if let existingRecognizer = recognizer, isRecognizerConfigured {
            print("[STT] Using existing recognizer")
            return existingRecognizer
        }

        print("[STT] Creating new recognizer...")

        // 更新状态：正在请求token
        state = .requestingToken

        // 获取配置
        do {
            let token = try await AudioTokenService.shared.getAzureToken()
            print("[STT] ✅ Got Azure token")

            // 更新状态：正在初始化
            state = .initializing

            let config = try connectionPool.getConfiguration(token: token, region: AzureSpeechConfig.serviceRegion)
            print("[STT] ✅ Got speech configuration")

            // 配置识别语言
            config.speechRecognitionLanguage = AzureSpeechConfig.defaultRecognitionLanguage

            // 使用交互模式获得更快响应
            if let mode = SPXPropertyId(rawValue: 5001) { // RecognitionMode property
                config.setPropertyTo("conversation", by: mode)
            }

            // 创建音频配置
            let audioConfig = SPXAudioConfiguration()

            // 创建识别器
            let newRecognizer = try SPXSpeechRecognizer(
                speechConfiguration: config,
                audioConfiguration: audioConfig
            )
            print("[STT] ✅ Created new recognizer")

            // 设置事件处理器
            setupRecognizerEvents(recognizer: newRecognizer)

            // 保存识别器
            recognizer = newRecognizer
            isRecognizerConfigured = true

            // 更新状态：准备就绪
            state = .ready

            return newRecognizer
        } catch {
            print("[STT] ❌ Failed to create recognizer: \(error)")
            state = .error("初始化失败")
            throw error
        }
    }

    private func setupRecognizerEvents(recognizer: SPXSpeechRecognizer) {
        // 部分结果
        recognizer.addRecognizingEventHandler { [weak self] _, event in
            Task { @MainActor in
                let partialText = event.result.text ?? ""
                print("[STT] Partial result: \(partialText)")
                self?.partialText = partialText
                self?.hasPendingFinalResult = !partialText.isEmpty
            }
        }

        // 最终结果
        recognizer.addRecognizedEventHandler { [weak self] _, event in
            Task { @MainActor in
                guard let text = event.result.text, !text.isEmpty else {
                    self?.hasPendingFinalResult = false
                    return
                }
                print("[STT] Final result: \(text)")
                self?.recognizedText = text
                self?.partialText = ""
                self?.hasPendingFinalResult = false

                // 如果有等待的完成回调，触发它
                if let completion = self?.stopCompletionHandler {
                    completion()
                    self?.stopCompletionHandler = nil
                }
            }
        }

        // 会话停止事件
        recognizer.addSessionStoppedEventHandler { [weak self] _, _ in
            Task { @MainActor in
                print("[STT] Session stopped")
                self?.hasPendingFinalResult = false

                // 确保完成回调被调用
                if let completion = self?.stopCompletionHandler {
                    completion()
                    self?.stopCompletionHandler = nil
                }
            }
        }

        // 错误处理
        recognizer.addCanceledEventHandler { [weak self] _, event in
            Task { @MainActor in
                print("[STT] Recognition canceled: \(event.errorDetails ?? "Unknown error")")
                self?.state = .error("识别被取消")
                self?.hasPendingFinalResult = false

                // 错误情况下也要调用完成回调
                if let completion = self?.stopCompletionHandler {
                    completion()
                    self?.stopCompletionHandler = nil
                }
            }
        }
    }

    func cleanup() async {
        if state == .listening {
            await stopRecognition()
        }

        stopRecordingTimer()
        recognizer = nil
        isRecognizerConfigured = false

        recognizedText = ""
        partialText = ""
        recordingDuration = 0
        hasPendingFinalResult = false
        stopCompletionHandler = nil
        state = .idle

        audioSessionManager.deactivateAfterDelay()
    }
}
