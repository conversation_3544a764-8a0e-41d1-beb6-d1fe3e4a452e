import Foundation

final class ChatService {
    // MARK: - 普通聊天请求（非流式）

    /// 发送聊天请求
    /// - Parameter req: 统一聊天请求参数
    /// - Returns: 聊天响应数据
    func sendChatMessage(req: UnifiedChatCompletionReq) async throws -> ChatCompletionPes {
        guard let parameters = req.toAPIParameters() else {
            throw NetworkError.unknown(NSError(
                domain: "ChatService",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "参数序列化失败"]
            ))
        }

        let res: Res<ChatCompletionPes> = try await Http.shared.POST(
            ChatApi.completions.path,
            body: parameters
        )
        return res.data
    }

    // MARK: - 流式聊天请求（SSE）

    /// 开始流式聊天
    /// - Parameter req: 统一聊天请求参数
    /// - Returns: AsyncStream流式响应
    @MainActor
    func startChatStream(req: UnifiedChatCompletionReq) -> AsyncStream<Res<ChatCompletionPes>> {
        guard let parameters = req.toAPIParameters() else {
            return AsyncStream { continuation in
                // 创建一个错误响应，使用可选的data字段
                let errorResponse: Res<ChatCompletionPes> = Res(
                    code: -1,
                    msg: "参数序列化失败",
                    data: ChatCompletionPes.text(code: -1, content: "参数序列化失败")
                )
                continuation.yield(errorResponse)
                continuation.finish()
            }
        }

        return EventSourceAdapter.shared.connect(
            ChatApi.completions.path,
            parameters: parameters
        )
    }

    // MARK: - 文件对话请求（RAG）

    /// 发送文件对话请求
    /// - Parameter req: 统一聊天请求参数
    /// - Returns: 聊天响应数据
    func sendFileChatMessage(req: UnifiedChatCompletionReq) async throws -> ChatCompletionPes {
        guard let parameters = req.toAPIParameters() else {
            throw NetworkError.unknown(NSError(
                domain: "ChatService",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "参数序列化失败"]
            ))
        }

        let res: Res<ChatCompletionPes> = try await Http.shared.POST(
            ChatApi.ragCompletions.path,
            body: parameters
        )
        return res.data
    }

    /// 开始文件对话流式聊天
    /// - Parameter req: 统一聊天请求参数
    /// - Returns: AsyncStream流式响应
    @MainActor
    func startFileChatStream(req: UnifiedChatCompletionReq) -> AsyncStream<Res<ChatCompletionPes>> {
        guard let parameters = req.toAPIParameters() else {
            return AsyncStream { continuation in
                // 创建一个错误响应，使用可选的data字段
                let errorResponse: Res<ChatCompletionPes> = Res(
                    code: -1,
                    msg: "参数序列化失败",
                    data: ChatCompletionPes.text(code: -1, content: "参数序列化失败")
                )
                continuation.yield(errorResponse)
                continuation.finish()
            }
        }

        return EventSourceAdapter.shared.connect(
            ChatApi.ragCompletions.path,
            parameters: parameters
        )
    }

    // MARK: - 图片对话请求

    /// 开始图片对话流式聊天
    func startImageChatStream(req: ImageGenerationRequest) -> AsyncStream<Res<ImageGenerationResponse>> {
        guard let parameters = try? req.toAPIParameters() else {
            return AsyncStream { continuation in
                let errorStatusResponse = ImageGenerationStatusResponse(
                    code: -1,
                    type: "status",
                    event: "error",
                    node: "parameter_serialization_failed"
                )
                let errorResponse: Res<ImageGenerationResponse> = Res(
                    code: -1,
                    msg: "图片生成请求参数序列化失败",
                    data: .status(errorStatusResponse)
                )
                continuation.yield(errorResponse)
                continuation.finish()
            }
        }

        return createOptimizedImageGenerationStream(
            endpoint: ChatApi.imageCompletions.path,
            parameters: parameters
        )
    }

    /// 图片生成请求
    private func createOptimizedImageGenerationStream(
        endpoint: String,
        parameters: [String: Any]
    ) -> AsyncStream<Res<ImageGenerationResponse>> {
        return AsyncStream { continuation in
            Task {
                defer {
                    continuation.finish()
                }

                let rawStream: AsyncStream<String> = await EventSourceAdapter.shared.connectRaw(
                    endpoint,
                    parameters: parameters
                )

                for await jsonString in rawStream {
                    if let response = ImageGenerationResponseHandler.parseImageGenerationResponse(from: jsonString) {
                        continuation.yield(response)

                        if isChainEndEvent(response) {
                            break
                        }
                    }
                }

                print("\(DateUtils.formatTimeOnly()) ==> ChatService: 图像生成数据流处理完成")
            }
        }
    }

    /// 检查是否为链结束事件
    private func isChainEndEvent(_ response: Res<ImageGenerationResponse>) -> Bool {
        switch response.data {
        case .status(let statusResponse):
            return statusResponse.event == "on_chain_end"
        case .imageGeneration:
            return false
        }
    }

    // MARK: - 图片识别对话请求

    @MainActor
    func startImageRecognitionChatStream(req: ImageRecognitionReq) -> AsyncStream<Res<ImageRecognitionRes>> {
        guard let parameters = req.toDictionary() else {
            return AsyncStream { continuation in
                let errorStatusResponse = ImageRecognitionStreamStatusRes(
                    code: -1,
                    type: "status",
                    event: "error",
                    node: "parameter_serialization_failed"
                )
                let errorResponse: Res<ImageRecognitionRes> = Res(
                    code: -1,
                    msg: "图片生成请求参数序列化失败",
                    data: .status(errorStatusResponse)
                )
                continuation.yield(errorResponse)
                continuation.finish()
            }
        }

        return EventSourceAdapter.shared.connect(
            ChatApi.imageRecognition.path,
            parameters: parameters
        )
    }

    // MARK: - 便利方法

    /// 创建智能体聊天请求
    /// - Parameters:
    ///   - modelId: 模型ID
    ///   - message: 消息内容
    ///   - threadId: 会话ID
    ///   - thinking: 是否启用思考模式
    ///   - networking: 是否启用联网
    ///   - parentId: 父消息ID（重试时使用）
    ///   - isFirst: 是否为首次对话
    /// - Returns: 统一聊天请求
    func createChatRequest(
        modelId: Int,
        message: String,
        threadId: String,
        thinking: Bool? = nil,
        networking: Bool? = nil,
        parentId: String? = nil,
        isFirst: Bool
    ) -> UnifiedChatCompletionReq {
        return UnifiedChatCompletionReq.createChatRequest(
            modelId: modelId,
            messages: message,
            threadId: threadId,
            thinking: thinking,
            networking: networking,
            parentId: parentId,
            isFirst: isFirst
        )
    }

    /// 创建知识库对话请求
    /// - Parameters:
    ///   - modelId: 模型ID
    ///   - categoryId: 知识库分类ID
    ///   - message: 消息内容
    ///   - threadId: 会话ID
    ///   - isFirst: 是否为首次对话
    /// - Returns: 统一聊天请求
    func createKnowledgeRequest(
        modelId: Int,
        categoryId: Int,
        message: String,
        threadId: String,
        parentId: String? = nil,
        isFirst: Bool
    ) -> UnifiedChatCompletionReq {
        return UnifiedChatCompletionReq.createKnowledgeRequest(
            modelId: modelId,
            categoryId: categoryId,
            messages: message,
            threadId: threadId,
            parentId: threadId,
            isFirst: isFirst
        )
    }

    /// 创建文件对话请求
    /// - Parameters:
    ///   - knowledgeId: 知识库ID
    ///   - message: 消息内容
    ///   - threadId: 会话ID
    ///   - isFirst: 是否为首次对话
    /// - Returns: 统一聊天请求
    func createFileRequest(
        knowledgeId: Int,
        message: String,
        threadId: String,
        parentId: String? = nil,
        isFirst: Bool
    ) -> UnifiedChatCompletionReq {
        return UnifiedChatCompletionReq.createFileRequest(
            knowledgeId: knowledgeId,
            messages: message,
            threadId: threadId,
            parentId: parentId,
            isFirst: isFirst
        )
    }

    /// 获取历史记录分页列表
    func getHistoryPage(current: Int = 1, size: Int = 10, graphId: String? = nil) async throws -> HistoryPageData {
        var params: [String: Any] = [:]

        params["current"] = current
        params["size"] = size

        if let graphId {
            params["graph_id"] = graphId
        }

        print("ChatService.getHistoryPage - 请求参数: \(params)")

        do {
            let historyData: HistoryPageData = try await Http.shared.GETPAGE(
                ChatApi.historyPage.path,
                params: params
            )

            return historyData
        } catch {
            print("ChatService.getHistoryPage 失败: \(error)")
            return HistoryPageData()
        }
    }

    /// 删除历史记录（支持单个和批量删除）
    /// - Parameter threadIds: 线程ID数组
    func deleteHistories(threadIds: [String]) async throws {
        let body = ["thread_ids": threadIds]

        do {
            let response: OnlyCodeRes = try await Http.shared.POST_SIMPLE(
                ChatApi.deleteHistory.path,
                body: body
            )

            if response.code != 200 {
                throw BusinessError(code: response.code, message: response.msg)
            }

        } catch {
            throw error
        }
    }

    /// 获取历史对话详情
    func getHistoryDetail(threadId: String, graphId: String) async throws -> [HistoryDetailItem] {
        let params = [
            "thread_id": threadId,
            "graph_id": graphId,
        ]

        do {
            // 直接获取数组响应，不包装在Res中
            let response: [HistoryDetailItem] = try await Http.shared.GET_ARRAY(
                ChatApi.historyDetail.path,
                params: params
            )
            return response
        } catch {
            throw error
        }
    }
}
