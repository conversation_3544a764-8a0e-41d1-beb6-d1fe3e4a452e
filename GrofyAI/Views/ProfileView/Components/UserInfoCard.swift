import FlowStacks
import Kingfisher
import MijickPopups
import SwiftUI

// MARK: - 用户信息卡片组件

struct UserInfoCard: View {
    @EnvironmentObject private var authStore: AuthStore
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager
    @Environment(\.colorScheme) private var colorScheme

    @State private var nickname = "尊敬的会员用户"


    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            if isAuthenticated {
                authenticatedUserInfo
                membershipInfo
            } else {
                unauthenticatedUserInfo
            }
        }
        .cornerRadius(DesignSystem.Rounded.md)
    }

    private var isAuthenticated: Bool {
        authStore.getAccessToken() != nil
    }

    private var memberLevel: String {
        return authStore.getMembershipDisplayName()
    }

    private var memberLevelName: String {
        return authStore.getMemberLevel() ?? MembershipLevel.free.rawValue
    }

    private var membershipLevel: MembershipLevel {
        return authStore.getMembershipLevel()
    }

    private var membershipIconColor: Color {
        switch membershipLevel {
        case .free:
            return DesignSystem.Colors.textSecondary
        case .vip:
            return DesignSystem.Colors.gradient
        case .pro:
            return DesignSystem.Colors.primary
        case .plus:
            return Color.orange
        case .ultra:
            return Color.purple
        }
    }

    // 获取积分信息
    private var credits: Int {
        authStore.getCredits()
    }

    private var premiumCredits: Int {
        authStore.getPremiumCredits()
    }

    // 格式化积分显示
    private func formatCredits(_ credits: Int) -> String {
        if credits >= 1000000 {
            return String(format: "%.1fM", Double(credits) / 1000000.0)
        } else {
            return "\(credits)"
        }
    }

    // MARK: - 已登录用户信息

    @ViewBuilder
    private var authenticatedUserInfo: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            userAvatar

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Button(action: {
                    Task {
                        await PaymentPopup().present()
                    }
                }) {
                    HStack {
                        Text(nickname)
                            .font(DesignSystem.Typography.headline)
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .lineLimit(1)
                            .truncationMode(.tail)
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }

                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 12))
                        .foregroundColor(membershipIconColor)

                    Text(memberLevel)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }

            Spacer()
        }
    }

    // MARK: - 未登录用户信息

    @ViewBuilder
    private var unauthenticatedUserInfo: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            userAvatar

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text("未登录")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("登录后查看更多")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()

            Button(action: {
                globalAuthManager.requestAuthentication()
            }) {
                Text("登录")
                    .font(DesignSystem.Typography.cardTitle)
                    .foregroundColor(.white)
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.vertical, DesignSystem.Spacing.sm)
                    .background(DesignSystem.Colors.primary)
                    .cornerRadius(DesignSystem.Rounded.sm)
            }
            .accessibilityLabel("登录账号")
        }
    }

    // MARK: - 用户头像

    @ViewBuilder
    private var userAvatar: some View {
        KFImage(
            URL(
                string: "https://files.getquicker.net/_sitefiles/_guides/66ac708c-895b-40bc-6d9c-08da16da9c3c/2023/08/14/162821_202824_2.jpg"
            )
        )
        .resizable()
        .placeholder {
            Image(systemName: "person.fill")
                .font(.system(size: 20))
                .foregroundColor(.white)
                .frame(width: 50, height: 50)
                .background(
                    LinearGradient(
                        colors: [DesignSystem.Colors.primary, DesignSystem.Colors.gradient],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .clipShape(Circle())
        }
        .scaledToFill()
        .frame(width: 50, height: 50)
        .clipShape(Circle())
    }

    // MARK: - 会员等级信息

    @ViewBuilder
    private var membershipInfo: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            pointsInfo

            creditsDisplay
        }
        .padding(DesignSystem.Spacing.lg)
        .background(
            Group {
                if colorScheme == .light {
                    Image("MembershipInfoBackground")
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } else {
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#1a1a2e").opacity(0.8),
                            Color(hex: "#16213e").opacity(0.8),
                            Color(hex: "#0f3460").opacity(0.8),
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                }
            }
        )
        .cornerRadius(DesignSystem.Rounded.sm)
    }

    // MARK: - 积分信息

    @ViewBuilder
    private var pointsInfo: some View {
        HStack {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text("会员等级")
                    .font(DesignSystem.Typography.headline)
                    .foregroundStyle(
                        LinearGradient(
                            gradient: Gradient(colors: colorScheme == .light ? [
                                Color(hex: "#8468EF"),
                                Color(hex: "#5F62FF"),
                                Color(hex: "#3761FF"),
                            ] : [
                                Color(hex: "#A589FF"),
                                Color(hex: "#8A8CFF"),
                                Color(hex: "#6B8AFF"),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                Text(membershipLevel == .free ? "升级后解锁更多功能" : "享受会员专属权益")
                    .font(DesignSystem.Typography.caption)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                    .foregroundStyle(
                        LinearGradient(
                            gradient: Gradient(colors: colorScheme == .light ? [
                                Color(hex: "#A090F2"),
                                Color(hex: "#8C85F5"),
                                Color(hex: "#6B87F2"),
                            ] : [
                                Color(hex: "#C8BFFF"),
                                Color(hex: "#B8B0FF"),
                                Color(hex: "#A8B0FF"),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            }

            Spacer()

            Button(action: handleUpgradeAction) {
                Text(authStore.isPaidMember() ? "续费" : "升级")
                    .font(.system(size: 15, weight: .semibold))
                    .padding(.horizontal, DesignSystem.Spacing.xxl)
                    .padding(.vertical, DesignSystem.Spacing.sm)
                    .background(
                        Group {
                            if colorScheme == .light {
                                Color.white
                            } else {
                                Color.white.opacity(0.15)
                            }
                        }
                    )
                    .cornerRadius(20)
                    .foregroundStyle(
                        LinearGradient(
                            gradient: Gradient(colors: colorScheme == .light ? [
                                Color(hex: "#7B6BEF"),
                                Color(hex: "#6B87F2"),
                            ] : [
                                Color(hex: "#B8A6FF"),
                                Color(hex: "#8A9AFF"),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            }
            .accessibilityLabel("查看积分详情")
        }
    }

    // MARK: - 积分展示信息

    @ViewBuilder
    private var creditsDisplay: some View {
        VStack {
            HStack {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image("IconStarPoints")
                        .resizable()
                        .scaledToFit()
                        .frame(height: 16)
                    Text("基础积分")
                        .font(DesignSystem.Typography.content)
                        .fontWeight(.light)
                    Text(formatCredits(credits))
                        .font(DesignSystem.Typography.content)
                        .fontWeight(.medium)
                }

                Rectangle()
                    .fill(DesignSystem.Colors.textHint)
                    .frame(width: 1, height: 14)
                    .cornerRadius(10)

                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image("IconStarPoints.high")
                        .resizable()
                        .scaledToFit()
                        .frame(height: 16)
                    Text("高级积分")
                        .font(DesignSystem.Typography.content)
                        .fontWeight(.light)
                    Text(formatCredits(premiumCredits))
                        .font(DesignSystem.Typography.content)
                        .fontWeight(.medium)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                navigator.push(Route.creditsHistory)
            }
        }
        .padding(.vertical, DesignSystem.Spacing.md)
        .padding(.horizontal, DesignSystem.Spacing.sm)
        .background(
            Group {
                if colorScheme == .light {
                    DesignSystem.Colors.backgroundCard
                } else {
                    Color.black.opacity(0.3)
                }
            }
        )
        .cornerRadius(DesignSystem.Rounded.sm)
    }

    // MARK: - 处理升级/续费按钮点击

    private func handleUpgradeAction() {
        if authStore.getAccessToken() != nil {
            if authStore.isPaidMember() {
                navigator.push(Route.payment)
            } else {
                navigator.push(Route.payment)
            }
        } else {
            globalAuthManager.requestAuthentication()
        }
    }
}


