import FlowStacks
import SwiftUI

struct ImageRecognitionChat: View {
    @EnvironmentObject var controller: ImageRecognitionChatController
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject var ttsService: TextToSpeechService
    @EnvironmentObject var sttService: SpeechRecognitionService

    @State private var keyboardHeight: CGFloat = 0
    @FocusState private var isInputFocused: Bool
    @State private var showSpeechInput = false

    // 内部状态
    @State private var isAtBottom = true
    @State private var isInitialLoad = true
    @State private var isUserScrolling = false
    @State private var isLargeDataset = false

    var threadId: String?

    init(threadId: String? = nil) {
        self.threadId = threadId
    }

    private var streamingContentIdentifier: String {
        let content = controller.streamingContent
        let reasoning = controller.streamingMessage?.reasoningContent ?? ""
        let searchCount = controller.streamingMessage?.searchResults?.count ?? 0
        return "\(content.count)-\(reasoning.count)-\(searchCount)"
    }

    var body: some View {
        ScrollViewReader { proxy in
            VStack(spacing: 0) {
                ScrollView {
                    chatContent
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .scrollDetector(isAtBottom: $isAtBottom, isUserScrolling: $isUserScrolling)

                    BottomDetector(isAtBottom: $isAtBottom)
                        .id("bottom-anchor")
                }
                .scrollDismissesKeyboard(.interactively)
                .overlay(alignment: .bottom) {
                    if !isAtBottom {
                        BackToBottomButton {
                            scrollToBottom(proxy: proxy, animated: false)
                        }
                        .padding(.bottom, DesignSystem.Spacing.sm)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .coordinateSpace(name: "scroll")
                .onAppear {
                    updateDatasetSize()
                    performInitialScrollToBottom(proxy: proxy)
                }
                .onChange(of: controller.stableMessages.count) { newCount in
                    updateDatasetSize()

                    if isInitialLoad, newCount > 0 {
                        DispatchQueue.main.async {
                            scrollToBottom(proxy: proxy, animated: false)
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            isInitialLoad = false
                        }
                    }

                    if !isInitialLoad {
                        performAutoScroll(proxy: proxy)
                    }
                }
                .onChange(of: streamingContentIdentifier) { _ in
                    handleStreamingContentChange(proxy: proxy)
                }
                .onChange(of: isInputFocused) { newValue in
                    if newValue, isAtBottom, !isUserScrolling {
                        DispatchQueue.main.asyncAfter(deadline: .now()) {
                            scrollToBottom(proxy: proxy, animated: true)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .layoutPriority(1)
                .contentShape(Rectangle())
                .onTapGesture {
                    if isInputFocused {
                        isInputFocused = false
                    }
                }

                ImageRecognitionInput(
                    inputText: $controller.inputText,
                    isInputFocused: $isInputFocused,
                    isLoading: controller.isLoading,
                    placeholder: "请输入...",
                    onSend: { _ in
                        controller.sendMessage()
                        scrollToBottom(proxy: proxy, animated: false)
                    },
                    onMicrophoneAction: {
                        showSpeechInput = true
                    },
                    onStop: {
                        controller.stopStreaming()
                    }
                )
                .layoutPriority(0)
            }
        }

        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.backgroundPage)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: { navigator.pop() })
            }
            ToolbarItem(placement: .topBarTrailing) {
                Image(systemName: "clock")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.system(size: 14))
                    .onTapGesture {
                        navigator.push(Route.imageRecognitionHistory)
                    }
            }
            ToolbarItem(placement: .principal) {
                Text("图片识别")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .requireAuthentication()
        .onAppear {
            // 当视图出现时，命令 Controller 开始上传图片
            if let threadId {
                controller.loadHistoryChat(threadId: threadId)
            } else {
                controller.uploadInitialImages()
            }
        }
        .onDisappear {
            if !controller.handlePickedIamges.isEmpty {
                controller.clearAllState()
                ToastManager.shared.clearToast()
            }

            Task {
                await ttsService.cleanup()
                await sttService.cleanup()
            }
        }
    }

    @ViewBuilder
    private var chatContent: some View {
        LazyVStack(spacing: 14) {
            ForEach(controller.stableMessages) { message in
                if !controller.isRetryingMessage(messageId: message.id) {
                    ChatItem(
                        message: message,
                        isLoading: false,
                        onCopy: { controller.copyMessage(message) },
                        onVariantChanged: { variantIndex in controller.switchMessageVariant(
                            messageId: message.id,
                            variantIndex: variantIndex
                        ) },
                        variantInfo: controller.getMessageVariantInfo(messageId: message.id),
                        imageRecognition: true
                    )
                    .id(message.id)
                }
            }

            streamingMessageContent
            EmptyView()
        }
    }

    @ViewBuilder
    private var streamingMessageContent: some View {
        if let streaming = controller.streamingMessage {
            let variantInfo = controller.isCurrentlyRetrying() ?
                controller.getRetryVariantInfo() : nil

            ChatItem(
                message: streaming,
                isLoading: controller.isLoading,
                onRetry: nil,
                onCopy: { controller.copyMessage(streaming) },
                onVariantChanged: { variantIndex in
                    if let retryingId = controller.currentRetryingMessageId {
                        controller.switchMessageVariant(
                            messageId: retryingId,
                            variantIndex: variantIndex
                        )
                    }
                },
                variantInfo: variantInfo,
                imageRecognition: true,
                streamingContent: controller.streamingContent.isEmpty ? nil : controller.streamingContent
            )
            .id(streaming.id)
            .transition(.opacity.combined(with: .move(edge: .bottom)))
        }
    }

    private func handleStreamingContentChange(proxy: ScrollViewProxy) {
        guard !isInitialLoad, shouldAutoScroll() else { return }

        withAnimation(.easeOut(duration: 0.15)) {
            proxy.scrollTo("bottom-anchor", anchor: .bottom)
        }
    }

    private func shouldAutoScroll() -> Bool {
        return isAtBottom && !isUserScrolling
    }

    // 展示重试按钮状状态
    private func shouldShowRetryButton(for message: ChatMessageModel) -> Bool {
        guard !message.isUser else { return false }

        if controller.streamingMessage != nil {
            if controller.isCurrentlyRetrying() {
                return !controller.isRetryingMessage(messageId: message.id) &&
                    isLastAIMessage(message, in: controller.stableMessages)
            } else {
                return false
            }
        }

        return isLastAIMessage(message, in: controller.stableMessages)
    }

    private func isLastAIMessage(_ message: ChatMessageModel, in messages: [ChatMessageModel]) -> Bool {
        guard !message.isUser else { return false }

        if let lastAIMessage = messages.last(where: { !$0.isUser }) {
            return lastAIMessage.id == message.id
        }

        return false
    }

    private func scrollToBottom(proxy: ScrollViewProxy, animated: Bool) {
        let anchorID = "bottom-anchor"
        let adaptiveDelay = calculateAdaptiveDelay()

        DispatchQueue.main.async {
            let scrollAction = {
                proxy.scrollTo(anchorID, anchor: .bottom)
            }

            if animated {
                withAnimation(.easeInOut(duration: 0.3)) {
                    scrollAction()
                }
            } else {
                scrollAction()
            }

            if isLargeDataset {
                DispatchQueue.main.asyncAfter(deadline: .now() + adaptiveDelay) {
                    proxy.scrollTo(anchorID, anchor: .bottom)
                }
            }
        }
    }

    private func calculateAdaptiveDelay() -> TimeInterval {
        if isLargeDataset {
            return 0.15
        } else if controller.stableMessages.count > 100 {
            return 0.08
        } else {
            return 0.05
        }
    }

    private func updateDatasetSize() {
        let totalMessages = controller.stableMessages
            .count + (controller.streamingMessage != nil ? 1 : 0)
        isLargeDataset = totalMessages > 500
    }

    private func performInitialScrollToBottom(proxy: ScrollViewProxy) {
        func checkDataAndScroll(attempt: Int = 1) {
            let hasMessages = !controller.stableMessages.isEmpty || controller.streamingMessage != nil

            if hasMessages {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.05 * Double(attempt)) {
                    scrollToBottom(proxy: proxy, animated: false)
                }
            } else if attempt < 10 {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    checkDataAndScroll(attempt: attempt + 1)
                }
            }
        }

        checkDataAndScroll()
    }

    private func performAutoScroll(proxy: ScrollViewProxy) {
        if let lastMessage = controller.stableMessages.last, lastMessage.isUser {
            scrollToBottom(proxy: proxy, animated: false)
            return
        }

        if isAtBottom, !isUserScrolling {
            scrollToBottom(proxy: proxy, animated: true)
        }
    }

    private func getLoadingText(for loadingType: ChatLoadingType) -> String {
        switch loadingType {
        case .streaming:
            return "正在分析图片..."
        case .loadingHistory:
            return "正在加载历史对话..."
        case .none:
            return ""
        default:
            return ""
        }
    }

    private func shouldShowSkeleton(for loadingType: ChatLoadingType) -> Bool {
        switch loadingType {
        case .loadingHistory:
            return true
        case .none, .streaming, .uploading:
            return false
        }
    }
}

private struct ImageRecognitionChatNavigationBar: View {
    let onBackTap: () -> Void
    @EnvironmentObject var navigator: FlowPathNavigator

    var body: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.lg) {
            Button(action: onBackTap) {
                Image(systemName: "chevron.backward")
                    .font(.system(size: 18))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            .buttonStyle(.plain)

            VStack(alignment: .leading, spacing: 2) {
                Text("图片识别")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
            }

            Spacer()

            Image(systemName: "clock")
                .foregroundStyle(DesignSystem.Colors.textPrimary)
                .font(.title3)
                .onTapGesture {
                    navigator.push(Route.imageRecognitionHistory)
                }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundPage)
        .overlay(
            Rectangle()
                .fill(DesignSystem.Colors.border)
                .frame(height: 0.5),
            alignment: .bottom
        )
    }
}
