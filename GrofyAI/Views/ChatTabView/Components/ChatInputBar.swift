import Combine
import SwiftUI

enum VoiceInteractionState {
    case idle
    case active
}

// MARK: - 统一状态管理结构体

struct InputState: Equatable {
    var isExpanded = false
    var voice: VoiceInteractionState = .idle
    var isRecording = false
    var recordingDuration: TimeInterval = 0
    var voiceText = ""
    var isConfirming = false
    var isManuallyExiting = false

    // MARK: - 状态转换方法（原子性操作）

    /// 切换到语音模式
    mutating func switchToVoiceMode() {
        voice = .active
        isExpanded = false
        voiceText = ""
        isRecording = false
        isConfirming = false
        isManuallyExiting = false
    }

    /// 切换到文本模式（展开状态）
    mutating func switchToTextMode() {
        voice = .idle
        isExpanded = true
        isRecording = false
        isConfirming = false
        voiceText = ""
        resetRecording()
    }

    /// 退出语音模式（紧凑状态）
    mutating func exitVoiceMode() {
        voice = .idle
        isExpanded = false
        isRecording = false
        isConfirming = false
        voiceText = ""
        isManuallyExiting = true
        resetRecording()
    }

    /// 展开输入框（原子性操作）
    mutating func expand() {
        guard !isExpanded else { return }
        voice = .idle
        isExpanded = true
        isRecording = false
        isConfirming = false
    }

    /// 收起输入框（原子性操作）
    mutating func collapse() {
        guard isExpanded else { return }
        voice = .idle
        isExpanded = false
        isRecording = false
        isConfirming = false
        voiceText = ""
    }

    /// 开始录音
    mutating func startRecording() {
        isRecording = true
        recordingDuration = 0
    }

    /// 停止录音
    mutating func stopRecording() {
        isRecording = false
    }

    /// 重置录音状态
    mutating func resetRecording() {
        recordingDuration = 0
    }

    /// 开始确认流程
    mutating func startConfirming() {
        isConfirming = true
    }

    /// 完成确认流程
    mutating func finishConfirming() {
        isConfirming = false
        isManuallyExiting = false
    }

    /// 更新录音时长
    mutating func updateRecordingDuration(_ duration: TimeInterval) {
        recordingDuration = duration
    }

    /// 更新语音文本
    mutating func updateVoiceText(_ text: String) {
        voiceText = text
    }

    // MARK: - 计算属性

    var isInVoiceMode: Bool {
        voice == .active
    }
}

// MARK: - 聊天输入框组件

struct ChatInputBar: View {
    @Binding var inputText: String
    let placeholder: String
    let onSend: (String) -> Void
    let onCameraAction: () -> Void
    let onVoiceAction: () -> Void
    let onPlusAction: () -> Void
    let onStop: (() -> Void)?

    @Binding var enableThinking: Bool
    @Binding var enableNetworking: Bool

    let onToggleThinking: () -> Void
    let onToggleNetworking: () -> Void

    let isLoading: Bool

    var isInputFocused: FocusState<Bool>.Binding

    // MARK: - 状态管理

    @State private var inputState = InputState()
    @Namespace private var inputTransition

    // 录音计时器
    @EnvironmentObject private var sttService: SpeechRecognitionService

    // 音频管理器
    @StateObject private var audioManager = AudioLevelManager()

    @ObservedObject private var toastManager = ToastManager.shared

    // MARK: - 认证相关环境对象

    @EnvironmentObject private var authStore: AuthStore
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    @State private var cancellables = Set<AnyCancellable>()

    private var voiceText: String {
        sttService.recognizedText.isEmpty ? sttService.partialText : sttService.recognizedText
    }

    // MARK: - 认证检查方法

    /// 检查用户认证状态，未登录时触发认证界面
    private func checkAuthenticationRequired() -> Bool {
        guard AuthStore.shared.hasValidToken() else {
            globalAuthManager.requestAuthentication()
            return false
        }
        return true
    }

    /// 相机
    private func handleCameraActionWithAuth() {
        guard checkAuthenticationRequired() else { return }
        onCameraAction()
    }

    /// 语音
    private func handleVoiceActionWithAuth() {
        guard checkAuthenticationRequired() else { return }
        onVoiceAction()
    }

    /// 加号
    private func handlePlusActionWithAuth() {
        guard checkAuthenticationRequired() else { return }
        onPlusAction()
    }

    /// 深度思考
    private func handleToggleThinkingWithAuth() {
        guard checkAuthenticationRequired() else { return }
        onToggleThinking()
    }

    /// 实时搜索
    private func handleToggleNetworkingWithAuth() {
        guard checkAuthenticationRequired() else { return }
        onToggleNetworking()
    }

    private var isExpanded: Bool {
        inputState.isExpanded
    }

    private var isInVoiceMode: Bool {
        inputState.isInVoiceMode
    }

    private var dynamicPlaceholder: String {
        switch inputState.voice {
        case .idle:
            return placeholder
        case .active:
            return inputState.isRecording ? "正在说话..." : "点击麦克风开始说话"
        }
    }

    init(
        inputText: Binding<String>,
        isInputFocused: FocusState<Bool>.Binding,
        enableThinking: Binding<Bool>,
        enableNetworking: Binding<Bool>,
        onToggleThinking: @escaping () -> Void,
        onToggleNetworking: @escaping () -> Void,
        isLoading: Bool = false,
        placeholder: String = "询问任何问题",
        onSend: @escaping (String) -> Void,
        onCameraAction: @escaping () -> Void = {},
        onVoiceAction: @escaping () -> Void = {},
        onPlusAction: @escaping () -> Void = {},
        onStop: (() -> Void)? = nil
    ) {
        _inputText = inputText
        self.isInputFocused = isInputFocused
        _enableThinking = enableThinking
        _enableNetworking = enableNetworking
        self.onToggleThinking = onToggleThinking
        self.onToggleNetworking = onToggleNetworking
        self.isLoading = isLoading
        self.placeholder = placeholder
        self.onSend = onSend
        self.onCameraAction = onCameraAction
        self.onVoiceAction = onVoiceAction
        self.onPlusAction = onPlusAction
        self.onStop = onStop
    }

    var body: some View {
        VStack(spacing: 0) {
            inputContainerView
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.bottom, DesignSystem.Spacing.xs)

            Rectangle()
                .fill(DesignSystem.Colors.backgroundInput)
                .frame(height: 0)
                .safeAreaInset(edge: .bottom) {
                    Rectangle()
                        .fill(DesignSystem.Colors.backgroundInput)
                        .frame(height: 8)
                }
        }
        .background(DesignSystem.Colors.backgroundInput)
        .fixedSize(horizontal: false, vertical: true)
        .animation(.easeOut(duration: 0.2), value: inputState.isExpanded)
        .onChange(of: isInputFocused.wrappedValue) { focused in
            if focused, !inputState.isExpanded {
                inputState.expand()
            } else if !focused, inputState.isExpanded {
                inputState.collapse()
            }
        }
        .onChange(of: sttService.recognizedText) { newText in
            if inputState.isInVoiceMode, !newText.isEmpty {
                inputState.updateVoiceText(newText)
            }
        }
        .onChange(of: sttService.state) { newState in
            if inputState.isInVoiceMode, !inputState.isManuallyExiting {
                switch newState {
                case .listening:
                    inputState.startRecording()
                case .error, .idle:
                    if !inputState.isManuallyExiting {
                        inputState.stopRecording()
                        handleSpeechServiceStateChange()
                    }
                default:
                    break
                }
            }
        }
        .onDisappear {
            cleanupVoiceResources()
        }
    }

    // MARK: - 输入框容器视图

    @ViewBuilder
    private var inputContainerView: some View {
        Group {
            if inputState.isExpanded {
                expandedLayoutView
                    .transition(.opacity)
            } else {
                compactLayoutView
                    .transition(.opacity)
            }
        }
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.lg)
        .onAppear {
            setupSpeechRecognitionFeedbackListener()
        }
        .onDisappear {
            removeSpeechRecognitionFeedbackListener()
        }
    }

    // MARK: - 语音识别反馈监听

    private func setupSpeechRecognitionFeedbackListener() {
        NotificationCenter.default.addObserver(
            forName: .speechRecognitionFeedback,
            object: nil,
            queue: .main
        ) { notification in
            guard let userInfo = notification.userInfo,
                  let type = userInfo["type"] as? String,
                  let message = userInfo["message"] as? String else { return }

            Task { @MainActor in
                if type == "shortRecording" {
                    ToastManager.shared.showWarning(message)
                } else if type == "emptyResult" {
                    ToastManager.shared.showInfo(message)
                }
            }
        }
    }

    private func removeSpeechRecognitionFeedbackListener() {
        NotificationCenter.default.removeObserver(
            self,
            name: .speechRecognitionFeedback,
            object: nil
        )
    }

    // MARK: - 语音状态监听

    private func handleSpeechServiceStateChange() {
        guard !inputState.isManuallyExiting else { return }

        if !sttService.isListening {
            if !inputState.isConfirming {
                let currentRecognizedText = sttService.recognizedText
                if inputState.isInVoiceMode, !currentRecognizedText.isEmpty {
                    handleSpeechRecognitionSuccess()
                }
            }
        }
    }

    private func handleSpeechRecognitionSuccess() {
        let recognizedText = sttService.getRecognizedTextAndClear()
        if !recognizedText.isEmpty {
            inputState.updateVoiceText(recognizedText)
            inputState.voice = .active
        }
    }

    // MARK: - 紧凑布局视图

    private var compactLayoutView: some View {
        HStack(spacing: 0) {
            if !inputState.isInVoiceMode {
                ImageRecognitionIconView()
                    .padding(.leading, DesignSystem.Spacing.md)
            }

            if inputState.voice == .active {
                VoiceWaveDisplay(
                    audioManager: audioManager,
                    onClose: handleExitVoiceMode,
                    onConfirm: handleConfirmVoiceInput,
                    isProcessing: inputState.isConfirming
                )
                .padding(.leading, DesignSystem.Spacing.sm)
                .padding(.trailing, DesignSystem.Spacing.sm)
            } else {
                TextField(dynamicPlaceholder, text: .constant(""))
                    .fontLG(weight: DesignSystem.FontWeight.regular)
                    .padding(.horizontal, DesignSystem.Spacing.sm)
                    .disabled(true)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if !inputState.isInVoiceMode {
                            handleContainerTap()
                        }
                    }
            }

            HStack(spacing: DesignSystem.Spacing.md) {
                if !inputState.isInVoiceMode {
                    InputButton(iconName: "IconInputMicrophone", action: handleEnterVoiceMode)
                }
            }
            .padding(.trailing, DesignSystem.Spacing.md)
        }
        .frame(minHeight: 44)
        .padding(.vertical, DesignSystem.Spacing.sm)
    }

    // MARK: - 展开布局视图

    private var expandedLayoutView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            TextField(placeholder, text: $inputText, axis: .vertical)
                .fontLG(weight: DesignSystem.FontWeight.regular)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .focused(isInputFocused)
                .lineLimit(2...6)
                .textFieldStyle(.plain)
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.lg)
                .frame(minHeight: 44, alignment: .topLeading)
                .disabled(isInVoiceMode)

            bottomButtonRow
        }
    }

    // MARK: - 可重用组件

    /// 底部按钮行
    @ViewBuilder
    private var bottomButtonRow: some View {
        HStack(spacing: 0) {
            leftButtonGroupForVertical

            Spacer()

            rightButtonGroup
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }

    /// 左侧按钮组
    @ViewBuilder
    private var leftButtonGroupForVertical: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            ToggleInputButton(
                iconName: "IconInputThinking",
                title: "深度思考",
                isSelected: $enableThinking,
                isEnabled: ChatSettingsManager.shared.isThinkingEnabled,
                action: handleToggleThinkingWithAuth
            )

            ToggleInputButton(
                iconName: "IconInputNetworking",
                title: "实时搜索",
                isSelected: $enableNetworking,
                isEnabled: ChatSettingsManager.shared.isNetworkingEnabled,
                action: handleToggleNetworkingWithAuth
            )

            // InputButton(iconName: "IconInputCamera", action: onCameraAction)

            // if inputText.isEmpty, !isInVoiceMode {
            // InputButton(iconName: "IconInputMicrophone", action: handleEnterVoiceMode)
            // InputButton(iconName: "IconInputVoice", action: onVoiceAction)
            // }
        }
    }

    /// 右侧发送/停止按钮组
    @ViewBuilder
    private var rightButtonGroup: some View {
        HStack(spacing: 12) {
            if isLoading {
                Button(action: handleStopAction) {
                    Image(systemName: "stop.circle.fill")
                        .foregroundColor(.red)
                        .titleSmallStyle()
                }
                .buttonStyle(.plain)
            } else {
                SendButton(inputText: $inputText, action: handleSendAction)
            }
        }
    }

    // MARK: - 语音模式按钮组件

    /// 语音确认按钮
    @ViewBuilder
    private var voiceControlButton: some View {
        if inputState.voice == .active {
            Button(action: handleConfirmVoiceInput) {
                Group {
                    if inputState.isConfirming {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "checkmark")
                            .font(.system(size: DesignSystem.FontSize.sm, weight: DesignSystem.FontWeight.bold))
                    }
                }
                .foregroundColor(.white)
                .padding(DesignSystem.Spacing.xs)
                .background(
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    inputState.isConfirming ? DesignSystem.Colors.primary.opacity(0.7) : DesignSystem
                                        .Colors.primary,
                                    inputState.isConfirming ? DesignSystem.Colors.primary.opacity(0.5) : DesignSystem
                                        .Colors.primary.opacity(0.8),
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
                .shadow(color: DesignSystem.Colors.primary.opacity(0.2), radius: 2, x: 0, y: 1)
                .scaleEffect(inputState.isConfirming ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: inputState.isConfirming)
            }
            .buttonStyle(.plain)
            .disabled(inputState.isConfirming)
        } else {
            EmptyView()
        }
    }

    /// 处理容器点击事件
    private func handleContainerTap() {
        guard checkAuthenticationRequired() else { return }
        guard !inputState.isExpanded, !inputState.isInVoiceMode else { return }

        // 使用原子性操作同时更新两个状态
        inputState.expand()
        isInputFocused.wrappedValue = true
    }

    /// 处理发送消息
    private func handleSendAction() {
        guard checkAuthenticationRequired() else { return }

        let isSendEnabled = !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        guard isSendEnabled, !isLoading else { return }

        let trimmedText = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        onSend(trimmedText)
        inputText = ""
    }

    /// 处理停止流式响应
    private func handleStopAction() {
        onStop?()
    }

    /// 进入语音模式
    private func handleEnterVoiceMode() {
        guard checkAuthenticationRequired() else { return }

        withAnimation(.easeOut(duration: 0.2)) {
            inputState.switchToVoiceMode()
        }

        // 启动音频监控
        audioManager.startMonitoring()

        Task {
            do {
                try await sttService.startContinuousRecognition()
            } catch {
                ToastManager.shared.showError("语音识别启动失败")
                // 如果启动失败，退出语音模式
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.exitVoiceMode()
                }
                audioManager.stopMonitoring()
            }
        }
    }

    /// 退出语音模式
    private func handleExitVoiceMode() {
        // ✅ 立即设置退出标志，防止竞态条件
        inputState.isManuallyExiting = true

        withAnimation(.easeOut(duration: 0.2)) {
            inputState.exitVoiceMode()
        }

        // 停止音频监控
        audioManager.stopMonitoring()

        Task.detached {
            await sttService.stopRecognition()

            await MainActor.run {
                inputState.finishConfirming()
            }
        }
    }

    /// 确认语音输入
    private func handleConfirmVoiceInput() {
        guard checkAuthenticationRequired() else { return }

        guard !inputState.isConfirming else { return }

        inputState.startConfirming()

        // 停止音频监控
        audioManager.stopMonitoring()

        Task {
            await sttService.stopRecognition {
                processVoiceInputConfirmation()
            }
        }
    }

    /// 处理语音输入确认的文本合并和状态切换
    private func processVoiceInputConfirmation() {
        let recognizedText = sttService.getRecognizedTextAndClear()

        DispatchQueue.main.async {
            inputState.finishConfirming()

            if recognizedText.isEmpty {
                ToastManager.shared.showInfo("未识别到有效内容")
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.exitVoiceMode()
                }
                audioManager.stopMonitoring()
            } else {
                inputText = recognizedText
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.switchToTextMode()
                }
                isInputFocused.wrappedValue = true
            }
        }
    }

    private func cleanupVoiceResources() {
        inputState.isManuallyExiting = true

        Task {
            await sttService.stopRecognition()

            await MainActor.run {
                inputState.exitVoiceMode()
                inputState.finishConfirming()
            }
        }
    }
}
