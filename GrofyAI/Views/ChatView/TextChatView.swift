import Combine
import FlowStacks
import King<PERSON>er
import MijickPopups
import SwiftUI

// MARK: - 文本聊天视图

struct TextChatView: View {
    @ObservedObject private var modelManager = ModelManager.shared
    @ObservedObject private var chatSettingsManager = ChatSettingsManager.shared
    @StateObject private var textChatController = TextChatController()

    let initialMessage: String?
    let threadId: String?

    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject var ttsService: TextToSpeechService
    @EnvironmentObject var sttService: SpeechRecognitionService
    @FocusState private var isInputFocused: Bool
    @State private var keyboardHeight: CGFloat = 0

    // 内部状态
    @State private var isAtBottom = true
    @State private var isInitialLoad = true
    @State private var isUserScrolling = false
    @State private var isLargeDataset = false

    private var streamingContentIdentifier: String {
        let content = textChatController.streamingContent
        let reasoning = textChatController.streamingMessage?.reasoningContent ?? ""
        let searchCount = textChatController.streamingMessage?.searchResults?.count ?? 0
        return "\(content.count)-\(reasoning.count)-\(searchCount)"
    }

    init(initialMessage: String? = nil, threadId: String? = nil) {
        self.initialMessage = initialMessage
        self.threadId = threadId
    }

    var body: some View {
        ScrollViewReader { proxy in
            VStack(spacing: 0) {
                ScrollView {
                    chatContent
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .scrollDetector(isAtBottom: $isAtBottom, isUserScrolling: $isUserScrolling)

                    BottomDetector(isAtBottom: $isAtBottom)
                        .id("bottom-anchor")
                }
                .scrollDismissesKeyboard(.interactively)
                .overlay(alignment: .bottom) {
                    if !isAtBottom {
                        BackToBottomButton {
                            scrollToBottom(proxy: proxy, animated: false)
                        }
                        .padding(.bottom, DesignSystem.Spacing.sm)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .coordinateSpace(name: "scroll")
                .onAppear {
                    updateDatasetSize()
                    performInitialScrollToBottom(proxy: proxy)
                }
                .onChange(of: textChatController.stableMessages.count) { newCount in
                    updateDatasetSize()

                    if isInitialLoad, newCount > 0 {
                        DispatchQueue.main.async {
                            scrollToBottom(proxy: proxy, animated: false)
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            isInitialLoad = false
                        }
                    }

                    if !isInitialLoad {
                        performAutoScroll(proxy: proxy)
                    }
                }
                .onChange(of: streamingContentIdentifier) { _ in
                    handleStreamingContentChange(proxy: proxy)
                }
                .onChange(of: isInputFocused) { newValue in
                    if newValue, isAtBottom, !isUserScrolling {
                        DispatchQueue.main.asyncAfter(deadline: .now()) {
                            scrollToBottom(proxy: proxy, animated: true)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .layoutPriority(1)
                .contentShape(Rectangle())
                .onTapGesture {
                    if isInputFocused {
                        isInputFocused = false
                    }
                }

                TextChatInputBar(
                    inputText: $textChatController.inputText,
                    isInputFocused: $isInputFocused,
                    enableThinking: $chatSettingsManager.enableThinking,
                    enableNetworking: $chatSettingsManager.enableNetworking,
                    isThinkingEnabled: chatSettingsManager.isThinkingEnabled,
                    isNetworkingEnabled: chatSettingsManager.isNetworkingEnabled,
                    isLoading: textChatController.isLoading,
                    placeholder: "输入消息...",
                    onSend: { _ in
                        textChatController.sendMessage()
                        scrollToBottom(proxy: proxy, animated: false)
                    },
                    onToggleThinking: {
                        textChatController.toggleThinking()
                    },
                    onToggleNetworking: {
                        textChatController.toggleNetworking()
                    },
                    onStop: {
                        textChatController.stopStreaming()
                    }
                )
                .layoutPriority(0)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(DesignSystem.Colors.backgroundPage)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    toolbarLeadingItem()
                }
                ToolbarItem(placement: .topBarTrailing) {
                    toolbarTrailingItem()
                }
                ToolbarItem(placement: .principal) {
                    toolbarPrincipalItem()
                }
            }
            .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
            .navigationBarBackButtonHidden()
            .navigationBarTitleDisplayMode(.inline)
            .requireAuthentication()
            .onAppear {
                if let threadId {
                    textChatController.loadHistoryChat(threadId: threadId)
                }
            }
            .onDisappear {
                textChatController.stopStreaming()
                textChatController.resetCurrentThread()
                ToastManager.shared.clearToast()

                SpeechServiceManager.shared.cleanupAllVoiceResources()

                Task {
                    await ttsService.cleanup()
                    await sttService.cleanup()
                }
            }
            .task {
                if let initialMessage, !initialMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    textChatController.processInitialMessage(initialMessage)
                }
            }
        }
    }

    @ViewBuilder
    private func toolbarLeadingItem() -> some View {
        BackButton(onBack: handleBackTap)
    }

    @ViewBuilder
    private func toolbarTrailingItem() -> some View {
        NewChatButton(onTap: handleNewChatTap)
    }

    @ViewBuilder
    private func toolbarPrincipalItem() -> some View {
        ModelSelectionButton.compact(onTap: showLLMModelSelectionPopup)
    }

    private func showLLMModelSelectionPopup() {
        Task {
            await LLMModelSelectionPopup()
                .present()
        }
    }

    private func handleBackTap() {
        navigator.pop()
    }

    private func handleNewChatTap() {
        textChatController.clearMessages()
    }

    /// 根据加载类型获取相应的加载文本
    private func getLoadingText(for loadingType: ChatLoadingType) -> String {
        switch loadingType {
        case .none, .uploading:
            return ""
        case .streaming:
            return "AI正在思考..."
        case .loadingHistory:
            return "加载中..."
        }
    }

    /// 根据加载类型判断是否应该显示骨架屏
    private func shouldShowSkeleton(for loadingType: ChatLoadingType) -> Bool {
        switch loadingType {
        case .none, .streaming, .uploading:
            return false
        case .loadingHistory:
            return true
        }
    }

    // MARK: - 聊天内容

    @ViewBuilder
    private var chatContent: some View {
        LazyVStack(spacing: 14) {
            ForEach(textChatController.stableMessages) { message in
                if !textChatController.isRetryingMessage(messageId: message.id) {
                    ChatItem(
                        message: message,
                        isLoading: false,
                        onRetry: shouldShowRetryButton(for: message) ?
                            { textChatController.retryMessage(messageId: message.id) } : nil,
                        onCopy: { textChatController.copyMessage(message) },
                        onVariantChanged: { variantIndex in textChatController.switchMessageVariant(
                            messageId: message.id,
                            variantIndex: variantIndex
                        ) },
                        variantInfo: textChatController.getMessageVariantInfo(messageId: message.id)
                    )
                    .id(message.id)
                }
            }

            streamingMessageContent
            bottomAnchorContent
        }
    }

    @ViewBuilder
    private var streamingMessageContent: some View {
        if let streaming = textChatController.streamingMessage {
            let variantInfo = textChatController.isCurrentlyRetrying() ?
                textChatController.getRetryVariantInfo() : nil

            ChatItem(
                message: streaming,
                isLoading: textChatController.isLoading,
                onRetry: nil,
                onCopy: { textChatController.copyMessage(streaming) },
                onVariantChanged: { variantIndex in
                    if let retryingId = textChatController.currentRetryingMessageId {
                        textChatController.switchMessageVariant(
                            messageId: retryingId,
                            variantIndex: variantIndex
                        )
                    }
                },
                variantInfo: variantInfo,
                streamingContent: textChatController.streamingContent.isEmpty ? nil : textChatController
                    .streamingContent
            )
            .id(streaming.id)
            .transition(.opacity.combined(with: .move(edge: .bottom)))
        }
    }

    @ViewBuilder
    private var bottomAnchorContent: some View {
        EmptyView()
    }

    // MARK: - 辅助方法

    private func shouldShowRetryButton(for message: ChatMessageModel) -> Bool {
        guard !message.isUser else { return false }

        if textChatController.streamingMessage != nil {
            if textChatController.isCurrentlyRetrying() {
                return !textChatController.isRetryingMessage(messageId: message.id) &&
                    isLastAIMessage(message, in: textChatController.stableMessages)
            } else {
                return false
            }
        }

        return isLastAIMessage(message, in: textChatController.stableMessages)
    }

    private func isLastAIMessage(_ message: ChatMessageModel, in messages: [ChatMessageModel]) -> Bool {
        guard !message.isUser else { return false }

        if let lastAIMessage = messages.last(where: { !$0.isUser }) {
            return lastAIMessage.id == message.id
        }

        return false
    }

    private func scrollToBottom(proxy: ScrollViewProxy, animated: Bool) {
        let anchorID = "bottom-anchor"

        DispatchQueue.main.async {
            let scrollAction = {
                proxy.scrollTo(anchorID, anchor: .bottom)
            }

            if animated {
                withAnimation(.easeInOut(duration: 0.3)) {
                    scrollAction()
                }
            } else {
                scrollAction()
            }

            if isLargeDataset {
                let adaptiveDelay = calculateAdaptiveDelay()
                DispatchQueue.main.asyncAfter(deadline: .now() + adaptiveDelay) {
                    if !isUserScrolling, isAtBottom {
                        proxy.scrollTo(anchorID, anchor: .bottom)
                    }
                }
            }
        }
    }

    private func performInitialScrollToBottom(proxy: ScrollViewProxy) {
        func checkDataAndScroll(attempt: Int = 1) {
            let hasMessages = !textChatController.stableMessages.isEmpty || textChatController.streamingMessage != nil

            if hasMessages {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.05 * Double(attempt)) {
                    scrollToBottom(proxy: proxy, animated: false)
                }
            } else if attempt < 10 {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    checkDataAndScroll(attempt: attempt + 1)
                }
            }
        }

        checkDataAndScroll()
    }

    private func performAutoScroll(proxy: ScrollViewProxy) {
        if let lastMessage = textChatController.stableMessages.last, lastMessage.isUser {
            scrollToBottom(proxy: proxy, animated: false)
            return
        }

        if shouldAutoScroll() {
            scrollToBottom(proxy: proxy, animated: true)
        }
    }

    private func handleStreamingContentChange(proxy: ScrollViewProxy) {
        guard !isInitialLoad, shouldAutoScroll() else { return }

        withAnimation(.easeOut(duration: 0.15)) {
            proxy.scrollTo("bottom-anchor", anchor: .bottom)
        }
    }

    private func shouldAutoScroll() -> Bool {
        return isAtBottom && !isUserScrolling
    }

    private func updateDatasetSize() {
        let totalMessages = textChatController.stableMessages
            .count + (textChatController.streamingMessage != nil ? 1 : 0)
        isLargeDataset = totalMessages > 500
    }

    private func calculateAdaptiveDelay() -> TimeInterval {
        if isLargeDataset {
            return 0.15
        } else if textChatController.stableMessages.count > 100 {
            return 0.08
        } else {
            return 0.05
        }
    }
}

// MARK: - 文本对话输入框

private struct TextChatInputBar: View {
    @Binding var inputText: String
    @FocusState.Binding var isInputFocused: Bool
    @Binding var enableThinking: Bool
    @Binding var enableNetworking: Bool
    let isThinkingEnabled: Bool
    let isNetworkingEnabled: Bool
    let isLoading: Bool
    let placeholder: String
    let onSend: (String) -> Void
    let onToggleThinking: () -> Void
    let onToggleNetworking: () -> Void
    let onStop: () -> Void

    @State private var cachedIsSendEnabled = false

    // MARK: - 语音功能状态管理

    @State private var inputState = InputState()
    @Namespace private var inputTransition

    // 语音服务
    @EnvironmentObject private var sttService: SpeechRecognitionService
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    @StateObject private var audioManager = AudioLevelManager()
    @ObservedObject private var toastManager = ToastManager.shared

    var body: some View {
        VStack(spacing: 0) {
            inputContainerView
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.sm)
                .padding(.bottom, DesignSystem.Spacing.xs)

            // 底部安全区域占位
            Rectangle()
                .fill(DesignSystem.Colors.backgroundInput)
                .frame(height: 0)
                .safeAreaInset(edge: .bottom) {
                    Rectangle()
                        .fill(DesignSystem.Colors.backgroundInput)
                        .frame(height: 8)
                }
        }
        .background(DesignSystem.Colors.backgroundInput)
        .fixedSize(horizontal: false, vertical: true)
        .onChange(of: inputText) { newText in
            let newIsEnabled = !newText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            if newIsEnabled != cachedIsSendEnabled {
                cachedIsSendEnabled = newIsEnabled
            }
        }
        .onChange(of: sttService.recognizedText) { newText in
            if inputState.isInVoiceMode, !newText.isEmpty {
                inputState.updateVoiceText(newText)
            }
        }
        .onChange(of: sttService.state) { newState in
            // ✅ 修复竞态条件：增强退出状态检查
            if inputState.isInVoiceMode, !inputState.isManuallyExiting {
                switch newState {
                case .listening:
                    inputState.startRecording()
                case .error, .idle:
                    // ✅ 双重检查确保不在退出过程中
                    if !inputState.isManuallyExiting {
                        inputState.stopRecording()
                        handleSpeechServiceStateChange()
                    }
                default:
                    break
                }
            }
        }
        .onDisappear {
            cleanupVoiceResources()
        }
    }

    // MARK: - 输入框容器视图

    @ViewBuilder
    private var inputContainerView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            if inputState.voice == .active {
                VoiceWaveDisplay(
                    audioManager: audioManager,
                    onClose: handleExitVoiceMode,
                    onConfirm: handleConfirmVoiceInput,
                    isProcessing: inputState.isConfirming
                )
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.lg)
                .frame(minHeight: 44)
            } else {
                textInputArea
            }

            bottomButtonRow
        }
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.lg)
    }

    // MARK: - 文本输入区域

    @ViewBuilder
    private var textInputArea: some View {
        TextField(placeholder, text: $inputText, axis: .vertical)
            .fontLG(weight: DesignSystem.FontWeight.regular)
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .focused($isInputFocused)
            .lineLimit(2...6)
            .textFieldStyle(.plain)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.lg)
            .padding(.bottom, DesignSystem.Spacing.xs)
            .frame(minHeight: 44, alignment: .topLeading)
            .fixedSize(horizontal: false, vertical: true)
            .contentShape(Rectangle())
            .onTapGesture {
                handleTextFieldTap()
            }
            .disabled(inputState.isInVoiceMode)
            .accessibilityAddTraits(.isButton)
            .accessibilityLabel("文本对话输入框")
            .accessibilityHint("点击开始对话")
    }

    // MARK: - 底部按钮行

    @ViewBuilder
    private var bottomButtonRow: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.md) {
            leftButtonGroup

            Spacer()

            rightButtonGroup
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.bottom, DesignSystem.Spacing.md)
        .opacity(inputState.isInVoiceMode ? 0.4 : 1.0)
        .allowsHitTesting(!inputState.isInVoiceMode)
    }

    // MARK: - 左侧按钮组

    @ViewBuilder
    private var leftButtonGroup: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            ToggleButton(
                iconName: "IconInputThinking",
                title: "深度思考",
                isSelected: enableThinking,
                isEnabled: isThinkingEnabled,
                action: onToggleThinking
            )

            ToggleButton(
                iconName: "IconInputNetworking",
                title: "实时搜索",
                isSelected: enableNetworking,
                isEnabled: isNetworkingEnabled,
                action: onToggleNetworking
            )

            // 添加语音输入按钮
            if inputText.isEmpty, !inputState.isInVoiceMode {
                InputButton(iconName: "IconInputMicrophone", action: handleEnterVoiceMode)
            }
        }
    }

    // MARK: - 右侧按钮组

    @ViewBuilder
    private var rightButtonGroup: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            if isLoading {
                Button(action: handleStopAction) {
                    Image(systemName: "stop.circle.fill")
                        .foregroundColor(.red)
                        .titleSmallStyle()
                }
                .buttonStyle(.plain)
            } else {
                TextChatSendButton(inputText: $inputText, action: handleSendAction)
            }
        }
    }

    /// 处理发送消息
    private func handleSendAction() {
        guard cachedIsSendEnabled, !isLoading else { return }

        let trimmedText = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        onSend(trimmedText)
        inputText = ""

        isInputFocused = false

        cachedIsSendEnabled = false
    }

    /// 处理停止流式响应
    private func handleStopAction() {
        onStop()
    }

    // MARK: - 语音功能方法

    /// 检查认证状态
    private func checkAuthenticationRequired() -> Bool {
        guard AuthStore.shared.hasValidToken() else {
            globalAuthManager.requestAuthentication()
            return false
        }
        return true
    }

    /// 进入语音模式
    private func handleEnterVoiceMode() {
        guard checkAuthenticationRequired() else { return }

        withAnimation(.easeOut(duration: 0.2)) {
            inputState.switchToVoiceMode()
        }

        // 启动音频监控
        audioManager.startMonitoring()

        Task {
            do {
                try await sttService.startContinuousRecognition()
            } catch {
                toastManager.showError("语音识别启动失败")
                // 如果启动失败，退出语音模式
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.exitVoiceMode()
                }
                audioManager.stopMonitoring()
            }
        }
    }

    /// 退出语音模式
    private func handleExitVoiceMode() {
        // ✅ 立即设置退出标志，防止竞态条件
        inputState.isManuallyExiting = true

        withAnimation(.easeOut(duration: 0.2)) {
            inputState.exitVoiceMode()
        }

        // 停止音频监控
        audioManager.stopMonitoring()

        Task.detached {
            await sttService.stopRecognition()

            await MainActor.run {
                inputState.finishConfirming()
            }
        }
    }

    /// 确认语音输入
    private func handleConfirmVoiceInput() {
        let recognizedText = sttService.getRecognizedTextAndClear()
        if !recognizedText.isEmpty {
            inputText = recognizedText
            handleSendAction()
        }
        handleExitVoiceMode()
    }

    /// 处理语音服务状态变化
    private func handleSpeechServiceStateChange() {
        guard !inputState.isManuallyExiting else { return }

        if !sttService.isListening {
            if !inputState.isConfirming {
                let currentRecognizedText = sttService.recognizedText
                if inputState.isInVoiceMode, !currentRecognizedText.isEmpty {
                    handleSpeechRecognitionSuccess()
                }
            }
        }
    }

    /// 处理语音识别成功
    private func handleSpeechRecognitionSuccess() {
        let recognizedText = sttService.getRecognizedTextAndClear()
        if !recognizedText.isEmpty {
            inputState.updateVoiceText(recognizedText)
            inputState.voice = .active
        }
    }

    /// 清理语音资源
    private func cleanupVoiceResources() {
        if inputState.isInVoiceMode {
            inputState.isManuallyExiting = true
            audioManager.stopMonitoring()
            Task.detached {
                await sttService.stopRecognition()

                await MainActor.run {
                    inputState.exitVoiceMode()
                    inputState.finishConfirming()
                }
            }
        }
    }

    /// 处理文本输入框点击事件
    private func handleTextFieldTap() {
        guard !isInputFocused else { return }

        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        withAnimation(.easeInOut(duration: 0.2)) {
            isInputFocused = true
        }
    }
}

// MARK: - 功能开关按钮组件

private struct ToggleButton: View {
    let iconName: String
    let title: String
    let isSelected: Bool
    let isEnabled: Bool
    let action: () -> Void

    init(iconName: String, title: String, isSelected: Bool, isEnabled: Bool = true, action: @escaping () -> Void) {
        self.iconName = iconName
        self.title = title
        self.isSelected = isSelected
        self.isEnabled = isEnabled
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(iconName)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 14, height: 14)
                    .foregroundColor(foregroundColor)

                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(foregroundColor)
            }
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .fill(backgroundColor)
            )
        }
        .buttonStyle(.plain)
        .disabled(!isEnabled)
        .opacity(isEnabled ? 1.0 : 0.7)
        .accessibilityAddTraits(.isButton)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
    }

    private var foregroundColor: Color {
        if !isEnabled {
            return DesignSystem.Colors.textTertiary
        }
        return isSelected ? .white : DesignSystem.Colors.textSecondary
    }

    private var backgroundColor: Color {
        if !isEnabled {
            return DesignSystem.Colors.backgroundInput
        }
        return isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.backgroundInput
    }

    private var accessibilityLabel: String {
        if !isEnabled {
            return "\(title)不可用"
        }
        return "\(title)\(isSelected ? "已开启" : "已关闭")"
    }

    private var accessibilityHint: String {
        if !isEnabled {
            return "\(title)功能当前不可用"
        }
        return "点击\(isSelected ? "关闭" : "开启")\(title)"
    }
}

// MARK: - 发送按钮组件

private struct TextChatSendButton: View {
    let action: () -> Void
    @Binding var inputText: String

    @State private var cachedIsEnabled = false
    @State private var cachedForegroundColor: Color
    @State private var cachedOpacity = 0.4

    init(inputText: Binding<String>, action: @escaping () -> Void) {
        _inputText = inputText
        self.action = action

        let isEnabled = !inputText.wrappedValue.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        _cachedIsEnabled = State(initialValue: isEnabled)
        _cachedForegroundColor = State(initialValue: isEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors
            .textSecondary
        )
        _cachedOpacity = State(initialValue: isEnabled ? 1.0 : 0.4)
    }

    var body: some View {
        Button(action: action) {
            Image("IconInputSend")
                .foregroundColor(cachedForegroundColor)
        }
        .buttonStyle(.plain)
        .disabled(!cachedIsEnabled)
        .opacity(cachedOpacity)
        .onChange(of: inputText) { text in
            let newIsEnabled = !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            if newIsEnabled != cachedIsEnabled {
                cachedIsEnabled = newIsEnabled
                cachedForegroundColor = newIsEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary
                cachedOpacity = newIsEnabled ? 1.0 : 0.4
            }
        }
    }
}
