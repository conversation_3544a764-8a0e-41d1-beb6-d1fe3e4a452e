import Combine
import PhotosUI
import SwiftUI
import UniformTypeIdentifiers

// MARK: - 图片对话输入框组件

struct ImageChatInputBar: View {
    @Binding var inputText: String
    @Binding var parameters: ImageGenerationParameters
    @Binding var uploadedImages: [String]
    @Binding var showParameters: Bool

    let shouldHideThumbnails: Bool
    let placeholder: String
    let onSend: (String) -> Void
    let onImageFileUpload: ((UploadImagesRes) -> Void)?
    let onImageRemove: ((String) -> Void)?
    let onMicrophoneAction: () -> Void
    let onParameterChange: (ImageGenerationParameters) -> Void
    let onStop: (() -> Void)?

    let isLoading: Bool
    var isInputFocused: FocusState<Bool>.Binding

    // MARK: - 状态管理

    @State private var inputState = InputState()
    @Namespace private var inputTransition

    // 图片上传相关状态
    @State private var showMultiImagePicker = false
    @State private var uploadingCount = 0

    // MARK: - 语音识别相关依赖

    // 语音识别服务
    @EnvironmentObject private var sttService: SpeechRecognitionService

    // 音频管理器
    @StateObject private var audioManager = AudioLevelManager()

    @ObservedObject private var toastManager = ToastManager.shared

    // MARK: - 认证相关环境对象

    @EnvironmentObject private var authStore: AuthStore
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    @State private var cancellables = Set<AnyCancellable>()

    // FileService 实例
    private let fileService = FileService()

    // 线程ID
    let threadId: String

    // MARK: - 计算属性

    private var voiceText: String {
        sttService.recognizedText.isEmpty ? sttService.partialText : sttService.recognizedText
    }

    private var dynamicPlaceholder: String {
        if inputState.isInVoiceMode {
            return voiceText.isEmpty ? "正在聆听..." : voiceText
        }
        return placeholder
    }

    // MARK: - 认证检查方法

    /// 检查用户认证状态，未登录时触发认证界面
    private func checkAuthenticationRequired() -> Bool {
        guard AuthStore.shared.hasValidToken() else {
            globalAuthManager.requestAuthentication()
            return false
        }
        return true
    }

    init(
        inputText: Binding<String>,
        parameters: Binding<ImageGenerationParameters>,
        uploadedImages: Binding<[String]>,
        isInputFocused: FocusState<Bool>.Binding,
        threadId: String,
        showParameters: Binding<Bool>,
        shouldHideThumbnails: Bool = false,
        isLoading: Bool = false,
        placeholder: String = "描述您想要生成的图片...",
        onSend: @escaping (String) -> Void,
        onImageFileUpload: ((UploadImagesRes) -> Void)? = nil,
        onImageRemove: ((String) -> Void)? = nil,
        onMicrophoneAction: @escaping () -> Void = {},
        onParameterChange: @escaping (ImageGenerationParameters) -> Void = { _ in },
        onStop: (() -> Void)? = nil
    ) {
        _inputText = inputText
        _parameters = parameters
        _uploadedImages = uploadedImages
        _showParameters = showParameters
        self.shouldHideThumbnails = shouldHideThumbnails
        self.isInputFocused = isInputFocused
        self.threadId = threadId
        self.isLoading = isLoading
        self.placeholder = placeholder
        self.onSend = onSend
        self.onImageFileUpload = onImageFileUpload
        self.onImageRemove = onImageRemove
        self.onMicrophoneAction = onMicrophoneAction
        self.onParameterChange = onParameterChange
        self.onStop = onStop
    }

    var body: some View {
        VStack(spacing: 0) {
            inputContainerView
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.sm)
                .padding(.bottom, DesignSystem.Spacing.xs)

            Rectangle()
                .fill(DesignSystem.Colors.backgroundInput)
                .frame(height: 0)
                .safeAreaInset(edge: .bottom) {
                    Rectangle()
                        .fill(DesignSystem.Colors.backgroundInput)
                        .frame(height: 8)
                }
        }
        .background(DesignSystem.Colors.backgroundInput)
        .fixedSize(horizontal: false, vertical: true)
        .sheet(isPresented: $showMultiImagePicker) {
            MultiImagePicker { images in
                handleImagesSelected(images)
            }
        }
        .onChange(of: sttService.recognizedText) { newText in
            if inputState.isInVoiceMode, !newText.isEmpty {
                inputState.updateVoiceText(newText)
            }
        }
        .onChange(of: sttService.state) { newState in
            if inputState.isInVoiceMode, !inputState.isManuallyExiting {
                switch newState {
                case .listening:
                    inputState.startRecording()
                case .error, .idle:
                    if !inputState.isManuallyExiting {
                        inputState.stopRecording()
                        handleSpeechServiceStateChange()
                    }
                default:
                    break
                }
            }
        }
        .onDisappear {
            cleanupVoiceResources()
        }
    }

    // MARK: - 输入框容器视图

    @ViewBuilder
    private var inputContainerView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            if !shouldHideThumbnails {
                UploadedImageThumbnailView(
                    uploadedImages: $uploadedImages,
                    onRemove: { imageUrl in
                        uploadedImages.removeAll { $0 == imageUrl }
                        onImageRemove?(imageUrl)
                    },
                    uploadingCount: uploadingCount
                )
            }

            textInputArea

            bottomButtonRow
        }
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.lg)
    }

    // MARK: - 参数配置区域

    @ViewBuilder
    private var parametersConfigurationView: some View {
        EmptyView()
    }

    // MARK: - 文本输入区域

    @ViewBuilder
    private var textInputArea: some View {
        if inputState.voice == .active {
            VoiceWaveDisplay(
                audioManager: audioManager,
                onClose: handleExitVoiceMode,
                onConfirm: handleConfirmVoiceInput,
                isProcessing: inputState.isConfirming
            )
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.lg)
            .padding(.bottom, DesignSystem.Spacing.xs)
            .frame(minHeight: 44, alignment: .center)
        } else {
            TextField(dynamicPlaceholder, text: $inputText, axis: .vertical)
                .fontLG(weight: DesignSystem.FontWeight.regular)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .focused(isInputFocused)
                .lineLimit(2...6)
                .textFieldStyle(.plain)
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.lg)
                .padding(.bottom, DesignSystem.Spacing.xs)
                .frame(minHeight: 44, alignment: .topLeading)
                .fixedSize(horizontal: false, vertical: true)
                .contentShape(Rectangle())
                .onTapGesture {
                    if !inputState.isInVoiceMode {
                        handleTextFieldTap()
                    }
                }
                .disabled(inputState.isInVoiceMode)
                .accessibilityAddTraits(.isButton)
                .accessibilityLabel("消息输入框")
                .accessibilityHint("点击开始输入消息")
        }
    }

    // MARK: - 底部按钮行

    @ViewBuilder
    private var bottomButtonRow: some View {
        HStack(spacing: 0) {
            leftButtonGroup

            Spacer()

            rightButtonGroup
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }

    // MARK: - 左侧按钮组

    @ViewBuilder
    private var leftButtonGroup: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Button(action: toggleParameters) {
                Image(systemName: "slider.horizontal.3")
                    .foregroundColor(showParameters ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
            }
            .buttonStyle(.plain)

            Button(action: handleImageUpload) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("编辑图片")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.horizontal, DesignSystem.Spacing.sm)
                .padding(.vertical, DesignSystem.Spacing.xs)
                .background(
                    RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                        .fill(Color.clear)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                        .stroke(DesignSystem.Colors.border, lineWidth: DesignSystem.BorderWidth.thin)
                )
            }
            .buttonStyle(.plain)

            if inputText.isEmpty, !inputState.isInVoiceMode {
                InputButton(iconName: "IconInputMicrophone", action: handleMicrophoneAction)
            }
        }
        .opacity(inputState.isInVoiceMode ? 0.4 : 1.0)
        .allowsHitTesting(!inputState.isInVoiceMode)
    }

    // MARK: - 右侧按钮组

    @ViewBuilder
    private var rightButtonGroup: some View {
        HStack(spacing: 12) {
            if isLoading {
                Button(action: handleStopAction) {
                    Image(systemName: "stop.circle.fill")
                        .foregroundColor(.red)
                        .titleSmallStyle()
                }
                .buttonStyle(.plain)
            } else {
                SendButton(
                    inputText: $inputText,
                    additionalDisableCondition: uploadingCount > 0,
                    action: handleSendAction
                )
            }
        }
    }

    // MARK: - 私有方法

    private func toggleParameters() {
        showParameters.toggle()
    }

    private func handleImageUpload() {
        showMultiImagePicker = true
    }

    // MARK: - 麦克风按钮处理

    private func handleMicrophoneAction() {
        if inputState.isInVoiceMode {
            handleExitVoiceMode()
        } else {
            handleEnterVoiceMode()
        }

        onMicrophoneAction()
    }

    // MARK: - 语音模式管理

    /// 进入语音模式
    private func handleEnterVoiceMode() {
        guard checkAuthenticationRequired() else { return }

        withAnimation(.easeOut(duration: 0.2)) {
            inputState.switchToVoiceMode()
        }

        // 启动音频监控
        audioManager.startMonitoring()

        Task {
            do {
                try await sttService.startContinuousRecognition()
            } catch {
                ToastManager.shared.showError("语音识别启动失败")
                // 如果启动失败，退出语音模式
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.exitVoiceMode()
                }
                audioManager.stopMonitoring()
            }
        }
    }

    /// 退出语音模式
    private func handleExitVoiceMode() {
        // ✅ 立即设置退出标志，防止竞态条件
        inputState.isManuallyExiting = true

        withAnimation(.easeOut(duration: 0.2)) {
            inputState.exitVoiceMode()
        }

        // 停止音频监控
        audioManager.stopMonitoring()

        Task.detached {
            await sttService.stopRecognition()

            await MainActor.run {
                inputState.finishConfirming()
            }
        }
    }

    /// 确认语音输入
    private func handleConfirmVoiceInput() {
        guard inputState.isInVoiceMode else { return }

        inputState.startConfirming()

        Task.detached {
            await sttService.stopRecognition()

            await MainActor.run {
                processVoiceInputConfirmation()
            }
        }
    }

    // MARK: - 语音状态监听

    private func handleSpeechServiceStateChange() {
        guard !inputState.isManuallyExiting else { return }

        if !sttService.isListening {
            if !inputState.isConfirming {
                let currentRecognizedText = sttService.recognizedText
                if inputState.isInVoiceMode, !currentRecognizedText.isEmpty {
                    handleSpeechRecognitionSuccess()
                }
            }
        }
    }

    private func handleSpeechRecognitionSuccess() {
        let recognizedText = sttService.getRecognizedTextAndClear()
        if !recognizedText.isEmpty {
            inputState.updateVoiceText(recognizedText)
            inputState.voice = .active
        }
    }

    /// 处理语音输入确认的文本合并和状态切换
    private func processVoiceInputConfirmation() {
        let recognizedText = sttService.getRecognizedTextAndClear()

        DispatchQueue.main.async {
            inputState.finishConfirming()

            if recognizedText.isEmpty {
                ToastManager.shared.showInfo("未识别到有效内容")
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.exitVoiceMode()
                }
                audioManager.stopMonitoring()
            } else {
                inputText = recognizedText
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.switchToTextMode()
                }
                isInputFocused.wrappedValue = true
            }
        }
    }

    private func cleanupVoiceResources() {
        inputState.isManuallyExiting = true

        Task {
            await sttService.stopRecognition()

            await MainActor.run {
                inputState.exitVoiceMode()
                inputState.finishConfirming()
            }
        }
    }

    /// 处理图片选择完成
    private func handleImagesSelected(_ images: [UIImage]) {
        Task {
            await uploadImages(images)
        }
    }

    /// 统一上传图片方法
    @MainActor
    private func uploadImages(_ images: [UIImage]) async {
        uploadingCount += images.count

        do {
            let imageData = images.enumerated().map { index, image in
                let imageName = "uploaded_image_\(Date().timeIntervalSince1970)_\(index).png"
                return (imageInfo: image, name: imageName)
            }

            let uploadResults = try await fileService.uploadImages(
                images: imageData,
                threadId: threadId,
                format: .png
            )

            var newImages = uploadedImages
            for result in uploadResults {
                newImages.append(result.url)

                onImageFileUpload?(result)

                print("✅ 图片上传成功: \(result.url)")
            }
            uploadedImages = newImages

        } catch {
            print("❌ 图片上传失败: \(error.localizedDescription)")

            if let uploadError = error as? ImageUploadPreProcessingError {
                ToastManager.shared.showError(uploadError.localizedDescription)
            } else {
                ToastManager.shared.showError("图片上传失败：\(error.localizedDescription)")
            }
        }

        uploadingCount -= images.count
    }

    private func handleSendAction() {
        let textIsValid = !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        let isUploadingImages = uploadingCount > 0
        guard textIsValid, !isLoading, !isUploadingImages else { return }

        let trimmedText = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        onSend(trimmedText)
        inputText = ""

        isInputFocused.wrappedValue = false
    }

    private func handleStopAction() {
        onStop?()
    }

    private func handleTextFieldTap() {
        guard !isInputFocused.wrappedValue else { return }

        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        isInputFocused.wrappedValue = true
    }
}

// MARK: - 多选图片选择器

struct MultiImagePicker: UIViewControllerRepresentable {
    var selectionLimit = 9
    let onImagesSelected: ([UIImage]) -> Void

    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.selectionLimit = selectionLimit
        configuration.filter = .images

        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: MultiImagePicker
        let maxFileSize = 10 * 1024 * 1024 // 10MB

        init(_ parent: MultiImagePicker) {
            self.parent = parent
        }

        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            picker.dismiss(animated: true)

            guard !results.isEmpty else { return }

            var images: [UIImage] = []
            var skippedCount = 0
            let group = DispatchGroup()

            for result in results {
                group.enter()

                // 先检查文件大小
                if result.itemProvider.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                    result.itemProvider
                        .loadFileRepresentation(forTypeIdentifier: UTType.image.identifier) { url, error in
                            defer { group.leave() }

                            guard let url else { return }

                            do {
                                let fileAttributes = try FileManager.default.attributesOfItem(atPath: url.path)
                                let fileSize = fileAttributes[.size] as? Int ?? 0

                                if fileSize > self.maxFileSize {
                                    DispatchQueue.main.async {
                                        skippedCount += 1
                                    }
                                    return
                                }

                                // 文件大小符合要求，加载图片
                                if let imageData = try? Data(contentsOf: url),
                                   let image = UIImage(data: imageData)
                                {
                                    DispatchQueue.main.async {
                                        images.append(image)
                                    }
                                }
                            } catch {
                                print("Error checking file size: \(error)")
                            }
                        }
                } else {
                    group.leave()
                }
            }

            group.notify(queue: .main) {
                if skippedCount > 0 {
                    let message = skippedCount == 1 ?
                        "已忽略1张超过10MB的图片，请选择小于10MB的图片" :
                        "已忽略\(skippedCount)张超过10MB的图片，请选择小于10MB的图片"
                    ToastManager.shared.showWarning(message)
                }
                if !images.isEmpty {
                    self.parent.onImagesSelected(images)
                }
            }
        }
    }
}
