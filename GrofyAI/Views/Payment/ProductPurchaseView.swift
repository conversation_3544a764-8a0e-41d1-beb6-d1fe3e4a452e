import MijickPopups
import StoreKit
import SwiftUI

// MARK: - 新版内购产品视图

struct ProductPurchaseView: View {
    @StateObject private var storeManager = StoreKitService.shared
    @EnvironmentObject private var authStore: AuthStore
    @Binding var selectedTab: Int
    let membershipTypes: [(name: String, productType: String, themeColor: Color, gradientColors: [Color])]
    @State private var selectedProduct: Product?
    @State private var isPurchasing = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var enableFreeTrial = false

    init(selectedTab: Binding<Int>, membershipTypes: [(name: String, productType: String, themeColor: Color, gradientColors: [Color])]) {
        self._selectedTab = selectedTab
        self.membershipTypes = membershipTypes
    }

    var body: some View {
        ZStack {
            DesignSystem.Colors.membershipPurchaseBackground
                .ignoresSafeArea()

            ViewThatFits(in: .vertical) {
                // 当内容适合屏幕时，不使用ScrollView
                VStack(spacing: 24) {
                    // 会员标签
                    membershipTag
                    
                    // 主标题
                    mainTitle
                    
                    // 会员权益卡片
                    benefitsCard

                    // 订阅选项
                    subscriptionOptions

                    // 升级按钮
                    upgradeButton

                    // 底部链接
                    bottomLinks
                }
                .padding()
                
                // 当内容超出屏幕时，使用ScrollView
                ScrollView {
                    VStack(spacing: 24) {
                        // 会员标签
                        membershipTag
                        
                        // 主标题
                        mainTitle
                        
                        // 会员权益卡片
                        benefitsCard

                        // 订阅选项
                        subscriptionOptions

                        // 升级按钮
                        upgradeButton

                        // 底部链接
                        bottomLinks
                    }
                    .padding()
                }
            }
            .gesture(
                DragGesture()
                    .onEnded { value in
                        let threshold: CGFloat = 50
                        withAnimation(.easeInOut(duration: 0.3)) {
                            if value.translation.width > threshold {
                                if selectedTab > 0 {
                                    selectedTab -= 1
                                    selectDefaultProduct()
                                }
                            } else if value.translation.width < -threshold {
                                if selectedTab < membershipTypes.count - 1 {
                                    selectedTab += 1
                                    selectDefaultProduct()
                                }
                            }
                        }
                    }
            )
        }
        .overlay(loadingOverlay)
        .alert("购买失败", isPresented: $showError) {
            Button("确定") {}
        } message: {
            Text(errorMessage)
        }
        .onAppear {
            selectDefaultProduct()
        }
        .onChange(of: selectedTab) { _ in
            selectDefaultProduct()
        }
    }


    // MARK: - 会员标签（卡片外）
    
    @ViewBuilder
    private var membershipTag: some View {
        let currentTheme = membershipTypes[selectedTab]
        
        HStack {
            Text(currentTheme.name)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(currentTheme.themeColor)
                .cornerRadius(12)
            
            Spacer()
        }
    }

    // MARK: - 主标题（卡片外）
    
    @ViewBuilder
    private var mainTitle: some View {
        HStack {
            Text("解锁手机端无限使用")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
        }
    }

    // MARK: - 会员权益卡片

    @ViewBuilder
    private var benefitsCard: some View {
        let currentTheme = membershipTypes[selectedTab]

        ZStack {
            // 背景装饰图片
            HStack {
                Spacer()
                VStack {
                    Spacer()
                    if currentTheme.productType == "ULTRA" {
                        Image("ImageMembershipUltraDiamond")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 100, height: 100)
                            .opacity(0.8)
                    } else if currentTheme.productType == "PLUS" {
                        Image("ImageMembershipProDiamond")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 100, height: 100)
                            .opacity(0.8)
                    } else if currentTheme.productType == "PRO" {
                        Image("ImageMembershipProDiamond")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 100, height: 100)
                            .opacity(0.8)
                    } else {
                        Image(systemName: "sparkle")
                            .font(.system(size: 80))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: currentTheme.gradientColors,
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .opacity(0.3)
                    }
                }
                .padding(.trailing, 20)
                .padding(.bottom, 20)
            }
            
            VStack(alignment: .leading, spacing: 0) {
                VStack(alignment: .leading, spacing: 16) {
                    benefitRow(iconName: getBenefitIcon(type: currentTheme.productType, benefit: "chat"), text: "无限聊天消息", badge: "仅限移动设备", themeColor: currentTheme.themeColor)
                    benefitRow(iconName: getBenefitIcon(type: currentTheme.productType, benefit: "response"), text: "回答来自", themeColor: currentTheme.themeColor)
                    benefitRow(iconName: getBenefitIcon(type: currentTheme.productType, benefit: "image"), text: "图像生成", themeColor: currentTheme.themeColor)
                    benefitRow(iconName: getBenefitIcon(type: currentTheme.productType, benefit: "document"), text: "智能总结文件 & 网页", themeColor: currentTheme.themeColor)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 20)
            }
        }

        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(currentTheme.themeColor.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - 权益图标获取
    
    private func getBenefitIcon(type: String, benefit: String) -> String {
        switch type {
        case "ULTRA":
            // 无限版使用Ultra图标（紫色）
            return "IconBenefitUltra\(benefit.capitalized)"
        case "PLUS":
            // 旗舰版使用Pro图标（绿色）
            return "IconBenefitPro\(benefit.capitalized)"
        case "PRO":
            // 专业版使用Ultra图标（蓝色）
            return "IconBenefitUltra\(benefit.capitalized)"
        default:
            return "IconBenefitUltra\(benefit.capitalized)"
        }
    }

    // MARK: - 权益行

    @ViewBuilder
    private func benefitRow(iconName: String, text: String, badge: String? = nil, themeColor: Color) -> some View {
        HStack(spacing: 12) {
            Image(iconName)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 24, height: 24)

            Text(text)
                .font(.system(size: 15))
                .foregroundColor(.white.opacity(0.9))

            if let badge {
                Text(badge)
                    .font(.system(size: 11))
                    .foregroundColor(themeColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(themeColor.opacity(0.2))
                    .cornerRadius(10)
            }

            Spacer()
        }
    }

    // MARK: - 订阅选项

    @ViewBuilder
    private var subscriptionOptions: some View {
        let membershipType = membershipTypes[selectedTab].productType
        let filteredProducts = storeManager.products.filter { product in
            GrofyProduct(rawValue: product.id)?.membershipType == membershipType
        }.sorted { first, second in
            guard let firstProduct = GrofyProduct(rawValue: first.id),
                  let secondProduct = GrofyProduct(rawValue: second.id)
            else {
                return false
            }
            return !firstProduct.isYearly && secondProduct.isYearly
        }

        VStack(spacing: 16) {
            ForEach(filteredProducts) { product in
                SubscriptionOptionCard(
                    product: product,
                    isSelected: selectedProduct?.id == product.id,
                    isPurchased: storeManager.isPurchased(GrofyProduct(rawValue: product.id) ?? .ultraMonthly),
                    themeColor: membershipTypes[selectedTab].themeColor,
                    onSelect: {
                        selectedProduct = product
                    }
                )
            }
        }
    }

    // MARK: - 升级按钮

    @ViewBuilder
    private var upgradeButton: some View {
        Button(action: {
            Task {
                if let product = selectedProduct {
                    await purchaseProduct(product)
                }
            }
        }) {
            HStack {
                Image("IconMembershipUpgrade")
                Text(enableFreeTrial ? "开始免费试用" : "立即升级")
                    .font(.system(size: 17, weight: .semibold))
            }
            .foregroundColor(.black)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: membershipTypes[selectedTab].gradientColors),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(25)
        }
        .disabled(selectedProduct == nil)
        .opacity(selectedProduct == nil ? 0.6 : 1)
    }

    // MARK: - 底部链接

    @ViewBuilder
    private var bottomLinks: some View {
        HStack(spacing: 16) {
            Button("隐私") {
                showWebView(url: AppConfig.Web.privacyPolicyURL, title: "隐私政策")
            }

            Text("|")
                .foregroundColor(.white.opacity(0.3))

            Button("条款") {
                showWebView(url: AppConfig.Web.termsOfServiceURL, title: "服务条款")
            }

            Text("|")
                .foregroundColor(.white.opacity(0.3))

            Button("恢复") {
                Task {
                    await storeManager.restorePurchases()
                }
            }
        }
        .font(.system(size: 14))
        .foregroundColor(.white.opacity(0.6))
    }

    // MARK: - 加载覆盖层

    @ViewBuilder
    private var loadingOverlay: some View {
        if storeManager.isLoading || isPurchasing {
            Color.black.opacity(0.5)
                .ignoresSafeArea()
                .overlay(
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                )
        }
    }

    private func showWebView(url: String, title: String) {
        guard let webURL = URL(string: url) else { return }
        Task {
            await WebViewPopup(url: webURL, title: title).present()
        }
    }

    private func selectDefaultProduct() {
        let membershipType = membershipTypes[selectedTab].productType
        let filteredProducts = storeManager.products.filter { product in
            GrofyProduct(rawValue: product.id)?.membershipType == membershipType
        }

        // 默认选择月度订阅
        selectedProduct = filteredProducts.first { product in
            if let grofyProduct = GrofyProduct(rawValue: product.id) {
                return !grofyProduct.isYearly
            }
            return false
        }
    }

    private func purchaseProduct(_ product: Product) async {
        isPurchasing = true
        defer { isPurchasing = false }

        do {
            if let transaction = try await storeManager.purchase(product) {
                print("购买成功: \(transaction.productID)")
            }
        } catch {
            errorMessage = "购买失败: \(error.localizedDescription)"
            showError = true
        }
    }
}

// MARK: - 订阅选项卡片

struct SubscriptionOptionCard: View {
    let product: Product
    let isSelected: Bool
    let isPurchased: Bool
    let themeColor: Color
    let onSelect: () -> Void

    private var isYearly: Bool {
        GrofyProduct(rawValue: product.id)?.isYearly ?? false
    }

    var body: some View {
        Button(action: onSelect) {
            ZStack(alignment: .topTrailing) {
                HStack {
                    Text(product.displayPrice)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)

                    if isPurchased {
                        Text("当前计划")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(themeColor)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(themeColor.opacity(0.2))
                            .cornerRadius(6)
                            .padding(.leading, 8)
                    }

                    Spacer()

                    Text(subscriptionPeriodText)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.6))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(isSelected ? 0.12 : 0.08))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    isSelected ? themeColor : Color.white.opacity(0.2),
                                    lineWidth: isSelected ? 1.5 : 1
                                )
                        )
                )

                if isYearly {
                    Text("推荐")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.black)
                        .padding(.horizontal, 10)
                        .padding(.vertical, 4)
                        .background(themeColor)
                        .cornerRadius(8)
                        .offset(x: -8, y: -10)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var subscriptionPeriodText: String {
        if let period = product.subscription?.subscriptionPeriod {
            switch period.unit {
            case .month:
                return period.value == 1 ? "月度订阅" : "每 \(period.value) 个月"
            case .year:
                return period.value == 1 ? "年度订阅" : "每 \(period.value) 年"
            default:
                return ""
            }
        }
        return ""
    }
}
