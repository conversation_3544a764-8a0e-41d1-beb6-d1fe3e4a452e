import Kingfisher
import MarkdownView
import MijickPopups
import SwiftMermaid
import Swift<PERSON>

// MARK: - 单条聊天消息组件

struct ChatItem: View {
    let message: ChatMessageModel
    let isLoading: Bool
    let onRetry: (() -> Void)?
    let onCopy: () -> Void
    let onVariantChanged: ((Int) -> Void)?
    let variantInfo: (current: Int, total: Int)?
    let imageRecognition: Bool
    let streamingContent: String?

    init(
        message: ChatMessageModel,
        isLoading: Bool = false,
        onRetry: (() -> Void)? = nil,
        onCopy: @escaping () -> Void = {},
        onVariantChanged: ((Int) -> Void)? = nil,
        variantInfo: (current: Int, total: Int)? = nil,
        imageRecognition: Bool = false,
        streamingContent: String? = nil
    ) {
        self.message = message
        self.isLoading = isLoading
        self.onRetry = onRetry
        self.onCopy = onCopy
        self.onVariantChanged = onVariantChanged
        self.variantInfo = variantInfo
        self.imageRecognition = imageRecognition
        self.streamingContent = streamingContent
    }

    var body: some View {
        HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
            if message.isUser {
                Spacer()
                UserMessageBubble(
                    message: message,
                    onCopy: onCopy,
                    imageRecognition: imageRecognition
                )
            } else {
                AIMessageBubble(
                    message: message,
                    isLoading: isLoading,
                    onRetry: onRetry,
                    onCopy: onCopy,
                    onVariantChanged: onVariantChanged,
                    variantInfo: variantInfo,
                    streamingContent: streamingContent
                )
                Spacer()
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - 用户消息气泡

private struct UserMessageBubble: View {
    let message: ChatMessageModel
    let onCopy: () -> Void
    let imageRecognition: Bool

    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
            // 1. 图片内容优先显示
            // 图片识别，图片预览
            if imageRecognition || message.chatMode == .vision {
                let attachments = Array(message.allAttachments)
                if !attachments.isEmpty {
                    VStack(alignment: .trailing, spacing: 0) {
                        ImageAttachmentView(attachments: attachments)
                    }
                    .background(DesignSystem.Colors.primary)
                    .clipShape(
                        UnevenRoundedRectangle(
                            topLeadingRadius: DesignSystem.Rounded.lg,
                            bottomLeadingRadius: DesignSystem.Rounded.lg,
                            bottomTrailingRadius: DesignSystem.Rounded.lg,
                            topTrailingRadius: 0
                        )
                    )
                }
            } else {
                if !message.allAttachments.isEmpty {
                    // 分离图片和非图片附件
                    let imageAttachments = message.allAttachments.filter(\.isImage)
                    let nonImageAttachments = message.allAttachments.filter { !$0.isImage }

                    VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                        // 图片附件使用九宫格布局
                        if !imageAttachments.isEmpty {
                            ImageGridView(
                                images: imageAttachments,
                                maxWidth: UIScreen.main.bounds.width * 0.7,
                                alignment: .trailing
                            ) { index in
                                // 图片点击处理
                                print("点击了第 \(index) 张图片")
                            }
                        }

                        // 非图片附件使用原有的列表形式
                        if !nonImageAttachments.isEmpty {
                            UserFileAttachmentsView(files: nonImageAttachments)
                        }
                    }
                }
            }

            // 2. 文本内容
            VStack(alignment: .trailing, spacing: 0) {
                if !message.content.isEmpty {
                    Text(message.content)
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(.white)
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .background(DesignSystem.Colors.primary)
                        .clipShape(
                            UnevenRoundedRectangle(
                                topLeadingRadius: DesignSystem.Rounded.lg,
                                bottomLeadingRadius: DesignSystem.Rounded.lg,
                                bottomTrailingRadius: DesignSystem.Rounded.lg,
                                topTrailingRadius: 0
                            )
                        )
                }
            }
        }
    }
}

struct ImageAttachmentView: View {
    @EnvironmentObject var controller: ImageRecognitionChatController

    // 你可以改变这个数组来测试不同的布局
    let attachments: [MessageFile]

    // --- 核心逻辑：根据图片数量计算布局 ---
    private var columns: [GridItem] {
        // 大于4张（九宫格）或 3张，都用3列布局
        if attachments.count > 4 || (attachments.count > 2 && attachments.count < 4) {
            return Array(repeating: .init(.flexible(), spacing: DesignSystem.Spacing.xs), count: 3)
        }
        // 正好4张（四宫格）
        if attachments.count == 4 || attachments.count == 2 {
            return Array(repeating: .init(.flexible(), spacing: DesignSystem.Spacing.xs), count: 2)
        }
        // 1张图片
        return [GridItem(.flexible())]
    }

    // 动态宽度
    private var imageContainerMaxWidth: CGFloat {
        if attachments.count == 1 {
            // 1张图片时，宽度为屏幕的50%
            return UIScreen.main.bounds.width * 0.5
        }
        // 其他情况（网格布局），宽度为屏幕的70%
        return UIScreen.main.bounds.width * 0.7
    }

    var body: some View {
        // 使用一个 HStack 将所有内容推到右侧
        if !attachments.isEmpty {
            LazyVGrid(columns: columns, spacing: DesignSystem.Spacing.xs) {
                ForEach(attachments) { attachment in
                    ImageCell(url: attachment.url, localImage: attachment.localImage)
                }
            }
            .padding(DesignSystem.Spacing.sm)
            .frame(maxWidth: imageContainerMaxWidth)
            .overlay(
                // 只有当 controller.uploadLoading 为 true 时，才显示这个蒙层
                Group {
                    if controller.uploadLoading {
                        // 1. 创建一个半透明的黑色背景作为蒙层
                        Color.black.opacity(0.5)

                        // 2. 在蒙层上方显示一个加载指示器
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white)) // 让指示器为白色
                            .scaleEffect(1.5) // (可选) 让指示器大一点，更显眼
                    }
                }
                // (可选) 为蒙层的出现和消失添加动画效果
                .animation(.easeInOut, value: controller.uploadLoading)
                // (可选) 确保蒙层有和网格一样的圆角
                .cornerRadius(DesignSystem.Rounded.sm) // 假设你的 DesignSystem 里有圆角定义
            )
        }
    }
}

// 提取出的图片单元格视图，保持代码整洁
struct ImageCell: View {
    let url: String
    let localImage: UIImage?
    @State private var hasLoadFailed = false

    var body: some View {
        Rectangle()
            .fill(Color.clear) // 1. 创建一个透明的矩形作为骨架
            .aspectRatio(1, contentMode: .fit) // 2. 强制这个骨架必须是正方形
            .overlay(
                // 3. 在这个已经确定是正方形的骨架上，覆盖我们的内容
                ZStack {
                    if let uiImage = localImage {
                        // 如果存在，直接显示它
                        Image(uiImage: uiImage)
                            .resizable()
                            .scaledToFill()

                    } else if hasLoadFailed {
                        // --- 加载失败视图 ---
                        VStack(spacing: 4) {
                            Spacer()
                            Image(systemName: "photo.fill")
                                .font(.largeTitle)
                                .foregroundColor(DesignSystem.Colors.textSecondary)

                            Text("加载失败，点击重试")
                                .font(.system(size: 5))
                                .foregroundColor(DesignSystem.Colors.textSecondary)

                            Spacer()
                        }
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.gray.opacity(0.1))
                        .onTapGesture {
                            hasLoadFailed = false
                        }

                    } else {
                        // --- 图片视图 ---
                        KFImage(URL(string: url))
                            .placeholder {
                                ProgressView()
                                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                                    .background(Color.gray.opacity(0.1))
                            }
                            .onFailure { _ in hasLoadFailed = true }
                            .resizable()
                            .scaledToFill() // 图片内容填满覆盖层
                    }
                }
            )
            .clipped() // 4. 裁剪掉所有超出这个正方形骨架的内容
            .cornerRadius(DesignSystem.Rounded.sm) // (可选) 添加圆角
    }
}

// MARK: - AI消息气泡

private struct AIMessageBubble: View {
    let message: ChatMessageModel
    let isLoading: Bool
    let onRetry: (() -> Void)?
    let onCopy: () -> Void
    let onVariantChanged: ((Int) -> Void)?
    let variantInfo: (current: Int, total: Int)?
    let streamingContent: String?

    private var modelInfo: LLMListRes? {
        if message.chatMode == .rag {
            return nil
        }

        guard let modelId = message.modelId else { return nil }
        return ModelManager.shared.getModelById(modelId)
    }

    // 判断是否为纯图片消息
    private var isPureImageMessage: Bool {
        return message.isPureMediaMessage
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            AIMessageContainer(
                message: message,
                modelInfo: modelInfo,
                isLoading: isLoading,
                onRetry: onRetry,
                onCopy: onCopy,
                onVariantChanged: onVariantChanged,
                variantInfo: variantInfo,
                streamingContent: streamingContent,
                isPureImageMessage: isPureImageMessage
            )
        }
        .if(!isPureImageMessage) { view in
            view
                .background(DesignSystem.Colors.backgroundCard)
                .clipShape(
                    UnevenRoundedRectangle(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: DesignSystem.Rounded.lg,
                        bottomTrailingRadius: DesignSystem.Rounded.lg,
                        topTrailingRadius: DesignSystem.Rounded.lg
                    )
                )
        }
    }
}

// MARK: - AI消息统一容器

private struct AIMessageContainer: View {
    let message: ChatMessageModel
    let modelInfo: LLMListRes?
    let isLoading: Bool
    let onRetry: (() -> Void)?
    let onCopy: () -> Void
    let onVariantChanged: ((Int) -> Void)?
    let variantInfo: (current: Int, total: Int)?
    let streamingContent: String?
    let isPureImageMessage: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: isPureImageMessage ? 0 : DesignSystem.Spacing.sm) {
            // 1. 模型信息头部 (仅在非纯图片消息中显示)
            if !isPureImageMessage, message.chatMode != .image {
                if let modelInfo {
                    AIModelInfoHeader(
                        modelInfo: modelInfo,
                        variantInfo: variantInfo,
                        onVariantChanged: onVariantChanged,
                        messageId: message.id
                    )
                } else if message.chatMode == .rag,
                          let variantInfo,
                          let onVariantChanged,
                          variantInfo.total > 1
                {
                    FileChatVariantHeader(
                        variantInfo: variantInfo,
                        onVariantChanged: onVariantChanged,
                        messageId: message.id
                    )
                }
            }

            // 2. 消息内容
            AIMessageContentView(
                message: message,
                modelInfo: modelInfo,
                isLoading: isLoading,
                onRetry: onRetry,
                onCopy: onCopy,
                streamingContent: streamingContent
            )

            // 3. 操作栏
            if !isPureImageMessage,
               !isLoading,
               message.type != .fileAnalysis,
               message.type != .trialEnded,
               message.chatMode != .image,
               !(streamingContent ?? message.content).isEmpty
            {
                MessageActionBar(
                    message: message,
                    onRetry: onRetry,
                    onCopy: onCopy
                )
            }
        }
        .if(!isPureImageMessage) { view in
            view
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(
                    .top,
                    message.chatMode != .image ? DesignSystem.Spacing.sm : DesignSystem.Spacing.md
                )
                .padding(.bottom, DesignSystem.Spacing.md)
        }
    }
}

// MARK: - AI消息内容视图

private struct AIMessageContentView: View {
    let message: ChatMessageModel
    let modelInfo: LLMListRes?
    let isLoading: Bool
    let onRetry: (() -> Void)?
    let onCopy: () -> Void
    let streamingContent: String?

    // 判断是否为纯图片消息
    private var isPureImageMessage: Bool {
        message.content.isEmpty &&
            message.reasoningContent?.isEmpty != false &&
            message.searchResults?.isEmpty != false &&
            message.nonImageAttachments.isEmpty &&
            !message.images.isNilOrEmpty
    }

    var body: some View {
        if isPureImageMessage {
            AIImageAttachmentsView(images: message.images!)
        } else {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                // 1. 思考内容
                if let reasoningContent = message.reasoningContent, !reasoningContent.isEmpty {
                    AIExpandableReasoningContainer(content: reasoningContent)
                }

                // 2. 搜索结果
                if let searchResults = message.searchResults, !searchResults.isEmpty {
                    AISearchResultsView(searchResults: searchResults)
                }

                // 3. 主要文本内容优先显示
                let contentToDisplay = streamingContent ?? message.content
                if !contentToDisplay.isEmpty || isLoading, message.type != .error, message.type != .fileAnalysis {
                    AIMarkdownContentView(content: contentToDisplay, isLoading: isLoading)
                }

                // 4. 图片附件在文本之后展示
                if let images = message.images, !images.isEmpty {
                    AIImageAttachmentsView(images: images)
                }

                // 5. 文件附件（排除图片类型）
                let nonImageFiles = message.nonImageAttachments
                if !nonImageFiles.isEmpty {
                    AIFileAttachmentsView(files: nonImageFiles)
                }

                // 6. 文件分析结果
                if message.type == .fileAnalysis {
                    AIFileAnalysisView(message: message, isLoading: isLoading)
                }

                // 7. 试用结束购买按钮
                if message.type == .trialEnded {
                    TrialEndedPurchaseButtonView()
                }

                // 8. 系统消息（仅显示事件类型，错误消息通过Toast显示）
                if message.type == .event {
                    AISystemMessageView(content: message.content, type: message.type)
                }
            }
        }
    }
}

// MARK: - AI Markdown内容视图

private struct AIMarkdownContentView: View {
    let content: String
    let isLoading: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
            MarkdownView(content)
                .markdownMathRenderingEnabled()
                .codeBlockStyle(.default(lightTheme: "atom-one-light", darkTheme: "atom-one-dark"))
                .font(DesignSystem.Typography.titleMedium, for: .h1)
                .font(DesignSystem.Typography.titleSmall, for: .h2)
                .font(DesignSystem.Typography.body.weight(.semibold), for: .h3)
                .font(DesignSystem.Typography.subheadline.weight(.semibold), for: .h4)
                .font(DesignSystem.Typography.subheadline, for: .h5)
                .font(DesignSystem.Typography.content.weight(.medium), for: .h6)
                .font(DesignSystem.Typography.content, for: .body)
                .markdownComponentSpacing(DesignSystem.Spacing.md)

            if isLoading {
                ProgressView()
                    .scaleEffect(0.6)
                    .padding(.leading, 2)
            }
        }
    }
}

// MARK: - AI思考内容视图

private struct AIReasoningContentView: View {
    let content: String

    var body: some View {
        HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
            Rectangle()
                .fill(DesignSystem.Colors.warning)
                .frame(width: 3)
                .clipShape(RoundedRectangle(cornerRadius: 1.5))

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(DesignSystem.Colors.warning)
                        .font(.caption)

                    Text("AI 思考过程")
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Text(content)
                    .font(DesignSystem.Typography.content)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.vertical, DesignSystem.Spacing.sm)
        .padding(.horizontal, DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.warning.opacity(0.05))
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.md))
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                .stroke(DesignSystem.Colors.warning.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - AI系统消息视图

private struct AISystemMessageView: View {
    let content: String
    let type: ChatMessageModel.MessageType

    var body: some View {
        HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
            Image(systemName: iconName)
                .foregroundColor(iconColor)
                .font(.caption)

            Text(content)
                .font(DesignSystem.Typography.content)
                .foregroundColor(textColor)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.vertical, DesignSystem.Spacing.sm)
        .padding(.horizontal, DesignSystem.Spacing.md)
        .background(backgroundColor)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.md))
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                .stroke(borderColor, lineWidth: 1)
        )
    }

    private var iconName: String {
        switch type {
        case .event:
            return "info.circle"
        case .error:
            return "exclamationmark.triangle"
        default:
            return "message"
        }
    }

    private var iconColor: Color {
        switch type {
        case .event:
            return DesignSystem.Colors.info
        case .error:
            return DesignSystem.Colors.error
        default:
            return DesignSystem.Colors.primary
        }
    }

    private var textColor: Color {
        switch type {
        case .error:
            return DesignSystem.Colors.error
        default:
            return DesignSystem.Colors.textPrimary
        }
    }

    private var backgroundColor: Color {
        switch type {
        case .event:
            return DesignSystem.Colors.info.opacity(0.05)
        case .error:
            return DesignSystem.Colors.error.opacity(0.05)
        default:
            return DesignSystem.Colors.backgroundCard
        }
    }

    private var borderColor: Color {
        switch type {
        case .event:
            return DesignSystem.Colors.info.opacity(0.2)
        case .error:
            return DesignSystem.Colors.error.opacity(0.2)
        default:
            return DesignSystem.Colors.border
        }
    }
}

// MARK: - AI可展开思考内容容器

private struct AIExpandableReasoningContainer: View {
    let content: String
    @State private var isExpanded = true

    var body: some View {
        AIExpandableReasoningView(
            content: content,
            isExpanded: $isExpanded
        )
    }
}

// MARK: - AI可展开思考内容视图

private struct AIExpandableReasoningView: View {
    let content: String
    @Binding var isExpanded: Bool
    @State private var hasAutoExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            VStack(alignment: .leading, spacing: 0) {
                HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
                    Rectangle()
                        .fill(DesignSystem.Colors.warning)
                        .frame(width: 3, height: 20)
                        .clipShape(RoundedRectangle(cornerRadius: 1.5))

                    Text(isExpanded ? "AI 思考过程" : "💭 查看AI思考过程")
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.caption2)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.easeInOut(duration: 0.25), value: isExpanded)
                }
                .padding(.vertical, DesignSystem.Spacing.sm)
                .padding(.horizontal, DesignSystem.Spacing.md)
                .contentShape(Rectangle())
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.25)) {
                        isExpanded.toggle()
                        // 用户手动操作后，标记为已经自动展开过，避免后续自动展开干扰用户意图
                        if !hasAutoExpanded {
                            hasAutoExpanded = true
                        }
                    }
                }

                if isExpanded {
                    VStack(alignment: .leading, spacing: 0) {
                        Rectangle()
                            .fill(DesignSystem.Colors.warning.opacity(0.3))
                            .frame(height: 1)
                            .padding(.horizontal, DesignSystem.Spacing.md)

                        HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
                            Rectangle()
                                .fill(DesignSystem.Colors.warning)
                                .frame(width: 3)
                                .clipShape(RoundedRectangle(cornerRadius: 1.5))

                            MarkdownView(content)
                                .markdownMathRenderingEnabled()
                                .codeBlockStyle(.default(lightTheme: "atom-one-light", darkTheme: "atom-one-dark"))
                                .font(DesignSystem.Typography.content, for: .body)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .padding(.horizontal, DesignSystem.Spacing.md)
                    }
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.95, anchor: .top)),
                        removal: .opacity.combined(with: .scale(scale: 0.95, anchor: .top))
                    ))
                }
            }
            .background(DesignSystem.Colors.warning.opacity(0.05))
            .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.md))
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                    .stroke(DesignSystem.Colors.warning.opacity(0.2), lineWidth: 1)
            )
        }
        .onChange(of: content) { _ in
            autoExpandIfNeeded()
        }
    }

    // MARK: - 自动展开逻辑

    private func autoExpandIfNeeded() {
        // 只有在以下条件都满足时才自动展开：
        // 1. 有思考内容
        // 2. 当前是收起状态
        // 3. 还没有自动展开过（避免用户手动收起后再次自动展开）
        if !content.isEmpty, !isExpanded, !hasAutoExpanded {
            withAnimation(.easeInOut(duration: 0.25)) {
                isExpanded = true
                hasAutoExpanded = true
            }
        }
    }
}

// MARK: - 用户附件视图

private struct UserFileAttachmentsView: View {
    let files: [MessageFile]
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .trailing, spacing: 0) {
            VStack(alignment: .leading, spacing: 0) {
                HStack(spacing: DesignSystem.Spacing.sm) {
                    Image(systemName: "paperclip")
                        .foregroundColor(.white.opacity(0.8))
                        .font(.caption)

                    Text("\(files.count) 个附件")
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(.white.opacity(0.9))
                        .lineLimit(1)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.white.opacity(0.8))
                        .font(.caption2)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.easeInOut(duration: 0.25), value: isExpanded)
                }
                .padding(.vertical, DesignSystem.Spacing.sm)
                .padding(.horizontal, DesignSystem.Spacing.md)
                .contentShape(Rectangle())
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.25)) {
                        isExpanded.toggle()
                    }
                }

                if isExpanded {
                    VStack(alignment: .leading, spacing: 0) {
                        Rectangle()
                            .fill(.white.opacity(0.3))
                            .frame(height: 1)
                            .padding(.horizontal, DesignSystem.Spacing.md)

                        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                            ForEach(files, id: \.id) { file in
                                UserFileAttachmentItem(file: file)
                            }
                        }
                        .padding(.vertical, DesignSystem.Spacing.sm)
                        .padding(.horizontal, DesignSystem.Spacing.md)
                    }
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.95, anchor: .top)),
                        removal: .opacity.combined(with: .scale(scale: 0.95, anchor: .top))
                    ))
                }
            }
            .background(DesignSystem.Colors.primary.opacity(0.9))
            .clipShape(
                UnevenRoundedRectangle(
                    topLeadingRadius: DesignSystem.Rounded.md,
                    bottomLeadingRadius: DesignSystem.Rounded.md,
                    bottomTrailingRadius: DesignSystem.Rounded.md,
                    topTrailingRadius: DesignSystem.Rounded.md
                )
            )
        }
        .frame(width: UIScreen.main.bounds.width * 0.5)
    }
}

// MARK: - 用户文件附件列表项

private struct UserFileAttachmentItem: View {
    let file: MessageFile

    var body: some View {
        Button(action: {
            if let url = URL(string: file.url) {
                UIApplication.shared.open(url)
            }
        }) {
            HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: file.iconName)
                        .foregroundColor(.white.opacity(0.8))
                        .font(.caption2)
                        .frame(width: 12, height: 12)

                    Text(file.name)
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                        .lineLimit(1)
                        .truncationMode(.middle)
                }

                Spacer()

                Text(file.displayName)
                    .font(DesignSystem.Typography.content)
                    .foregroundColor(.white.opacity(0.7))
                    .lineLimit(1)
            }
            .padding(.vertical, DesignSystem.Spacing.xs)
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color.clear)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm))
    }
}

// MARK: - AI搜索结果视图

private struct AISearchResultsView: View {
    let searchResults: [SearchResult]
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            VStack(alignment: .leading, spacing: 0) {
                HStack(spacing: DesignSystem.Spacing.sm) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(DesignSystem.Colors.info)
                        .font(.caption)

                    Text("\(searchResults.count) 个来源")
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.caption2)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.easeInOut(duration: 0.25), value: isExpanded)
                }
                .padding(.vertical, DesignSystem.Spacing.sm)
                .padding(.horizontal, DesignSystem.Spacing.md)
                .contentShape(Rectangle())
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.25)) {
                        isExpanded.toggle()
                    }
                }

                if isExpanded {
                    VStack(alignment: .leading, spacing: 0) {
                        Rectangle()
                            .fill(DesignSystem.Colors.info.opacity(0.3))
                            .frame(height: 1)
                            .padding(.horizontal, DesignSystem.Spacing.md)

                        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                            ForEach(searchResults) { result in
                                AISearchResultItem(result: result)
                            }
                        }
                        .padding(.vertical, DesignSystem.Spacing.sm)
                        .padding(.horizontal, DesignSystem.Spacing.md)
                    }
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.95, anchor: .top)),
                        removal: .opacity.combined(with: .scale(scale: 0.95, anchor: .top))
                    ))
                }
            }
            .background(DesignSystem.Colors.info.opacity(0.05))
            .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.md))
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                    .stroke(DesignSystem.Colors.info.opacity(0.2), lineWidth: 1)
            )
        }
    }
}

// MARK: - AI搜索结果列表项

private struct AISearchResultItem: View {
    let result: SearchResult

    var body: some View {
        Button(action: {
            if let url = URL(string: result.url) {
                UIApplication.shared.open(url)
            }
        }) {
            HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
                Image(systemName: "link")
                    .foregroundColor(DesignSystem.Colors.info)
                    .font(.caption2)
                    .frame(width: 12, height: 12)

                Text(result.title)
                    .font(DesignSystem.Typography.content)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)

                Spacer()

                Image(systemName: "arrow.up.right")
                    .foregroundColor(DesignSystem.Colors.info)
                    .font(.caption2)
            }
            .padding(.vertical, DesignSystem.Spacing.xs)
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color.clear)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm))
    }
}

// MARK: - 文件对话变体头部

private struct FileChatVariantHeader: View {
    let variantInfo: (current: Int, total: Int)
    let onVariantChanged: (Int) -> Void
    let messageId: UUID

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            Text("RAG对话")
                .font(DesignSystem.Typography.content)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .lineLimit(1)

            Spacer()

            MessageVariantSelector(
                messageId: messageId,
                currentIndex: variantInfo.current,
                totalCount: variantInfo.total,
                onVariantChanged: onVariantChanged
            )
        }
    }
}

// MARK: - AI模型信息头部

private struct AIModelInfoHeader: View {
    let modelInfo: LLMListRes
    let variantInfo: (current: Int, total: Int)?
    let onVariantChanged: ((Int) -> Void)?
    let messageId: UUID

    var body: some View {
        HStack(spacing: 6) {
            ModelIconView(
                iconURL: modelInfo.icon,
                provider: modelInfo.provider,
                size: 22,
                cornerRadius: 4
            )

            Text(modelInfo.safeName)
                .font(.system(size: 16))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)

            Spacer()

            if let variantInfo,
               let onVariantChanged,
               variantInfo.total > 1
            {
                MessageVariantSelector(
                    messageId: messageId,
                    currentIndex: variantInfo.current,
                    totalCount: variantInfo.total,
                    onVariantChanged: onVariantChanged
                )
            }
        }
    }
}

// MARK: - 自定义代码块样式

private struct CustomCodeBlockStyle: CodeBlockStyle {
    var lineSpacing: CGFloat = 8

    func makeBody(configuration: Configuration) -> some View {
        ScrollView(.horizontal) {
            Text(configuration.code)
        }
        .lineSpacing(lineSpacing)
        .font(.system(.body, design: .monospaced))
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(.background)
        .clipShape(.rect(cornerRadius: 12))
        .overlay {
            RoundedRectangle(cornerRadius: 12)
                .stroke(.quaternary)
        }
    }
}

// MARK: - AI文件附件视图

private struct AIFileAttachmentsView: View {
    let files: [MessageFile]
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 统一容器，包含标题和内容
            VStack(alignment: .leading, spacing: 0) {
                // 文件附件标题（整个区域可点击）
                HStack(spacing: DesignSystem.Spacing.sm) {
                    Image(systemName: "paperclip")
                        .foregroundColor(DesignSystem.Colors.info)
                        .font(.caption)

                    Text("\(files.count) 个附件")
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.caption2)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.easeInOut(duration: 0.25), value: isExpanded)
                }
                .padding(.vertical, DesignSystem.Spacing.sm)
                .padding(.horizontal, DesignSystem.Spacing.md)
                .contentShape(Rectangle())
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.25)) {
                        isExpanded.toggle()
                    }
                }

                // 文件附件列表（平滑展开/收缩）
                if isExpanded {
                    VStack(alignment: .leading, spacing: 0) {
                        // 分隔线
                        Rectangle()
                            .fill(DesignSystem.Colors.info.opacity(0.3))
                            .frame(height: 1)
                            .padding(.horizontal, DesignSystem.Spacing.md)

                        // 内容区域
                        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                            ForEach(files, id: \.id) { file in
                                AIFileAttachmentItem(file: file)
                            }
                        }
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .padding(.horizontal, DesignSystem.Spacing.md)
                    }
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.95, anchor: .top)),
                        removal: .opacity.combined(with: .scale(scale: 0.95, anchor: .top))
                    ))
                }
            }
            .background(DesignSystem.Colors.info.opacity(0.05))
            .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.md))
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                    .stroke(DesignSystem.Colors.info.opacity(0.2), lineWidth: 1)
            )
        }
    }
}

// MARK: - AI文件附件项

private struct AIFileAttachmentItem: View {
    let file: MessageFile

    var body: some View {
        Button(action: {
            if let url = URL(string: file.url) {
                UIApplication.shared.open(url)
            }
        }) {
            HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
                Image(systemName: file.iconName)
                    .foregroundColor(DesignSystem.Colors.info)
                    .font(.caption2)
                    .frame(width: 12, height: 12)

                VStack(alignment: .leading, spacing: 2) {
                    Text(file.name)
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                        .lineLimit(1)
                        .truncationMode(.middle)

                    Text(file.displayName)
                        .font(DesignSystem.Typography.content)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                }

                Spacer()

                Image(systemName: "arrow.up.right")
                    .foregroundColor(DesignSystem.Colors.info)
                    .font(.caption2)
            }
            .padding(.vertical, DesignSystem.Spacing.xs)
            .padding(.horizontal, DesignSystem.Spacing.sm)
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color.clear)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm))
    }
}

// MARK: - AI文件分析视图

private struct AIFileAnalysisView: View {
    let message: ChatMessageModel
    let isLoading: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            let selectedTab = message.selectedAnalysisTab ?? .summary
            let hasContent = !message.content.isEmpty

            if hasContent || !isLoading {
                FileAnalysisContentDisplay(
                    message: message,
                    selectedTab: selectedTab,
                    isStreaming: isLoading
                )
            } else {
                FileAnalysisSkeletonView(selectedTab: selectedTab)
                    .transition(.opacity.combined(with: .scale))
                    .animation(.easeInOut(duration: 0.3), value: isLoading)
            }
        }
    }
}

// MARK: - 文件分析内容显示

private struct FileAnalysisContentDisplay: View {
    let message: ChatMessageModel
    let selectedTab: FileAnalysisTab
    let isStreaming: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            if !message.content.isEmpty {
                // 显示分析内容
                MarkdownView(message.content)
                    .markdownMathRenderingEnabled()
                    .codeBlockStyle(.default(lightTheme: "atom-one-light", darkTheme: "atom-one-dark"))
                    .font(DesignSystem.Typography.titleMedium, for: .h1)
                    .font(DesignSystem.Typography.titleSmall, for: .h2)
                    .font(DesignSystem.Typography.body.weight(.semibold), for: .h3)
                    .font(DesignSystem.Typography.subheadline.weight(.semibold), for: .h4)
                    .font(DesignSystem.Typography.subheadline, for: .h5)
                    .font(DesignSystem.Typography.content.weight(.medium), for: .h6)
                    .font(DesignSystem.Typography.content, for: .body)
                    .markdownComponentSpacing(DesignSystem.Spacing.md)
                    .textSelection(.enabled)
                    .animation(.easeInOut(duration: 0.2), value: message.content)
                    .id("analysis-content-\(selectedTab.rawValue)-\(message.content.hashValue)")
            } else if isStreaming {
                VStack(spacing: DesignSystem.Spacing.md) {
                    ProgressView()
                        .scaleEffect(1.2)

                    Text("正在生成\(selectedTab.displayName)...")
                        .contentTextStyle()
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, DesignSystem.Spacing.xl)
            } else {
                VStack(spacing: DesignSystem.Spacing.md) {
                    Image(systemName: getEmptyStateIcon(for: selectedTab))
                        .font(.system(size: 32))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("暂无\(selectedTab.displayName)内容")
                        .contentTextStyle()
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, DesignSystem.Spacing.xl)
            }
        }
    }

    /// 获取空状态图标
    private func getEmptyStateIcon(for tab: FileAnalysisTab) -> String {
        switch tab {
        case .summary:
            return "text.alignleft"
        case .keyPoints:
            return "key"
        case .outline:
            return "list.bullet.indent"
        case .preview:
            return "doc.text"
        }
    }
}

// MARK: - 文件分析骨架屏视图

private struct FileAnalysisSkeletonView: View {
    let selectedTab: FileAnalysisTab

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            // 根据不同标签页显示不同的骨架屏布局
            switch selectedTab {
            case .summary:
                SummarySkeletonView()
            case .keyPoints:
                KeyPointsSkeletonView()
            case .outline:
                OutlineSkeletonView()
            case .preview:
                PreviewSkeletonView()
            }
        }
        .redacted(reason: .placeholder)
        .animation(.easeInOut(duration: 0.3), value: selectedTab)
    }
}

// MARK: - 预览骨架屏

private struct PreviewSkeletonView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            // 文件图标和名称骨架
            HStack(spacing: DesignSystem.Spacing.sm) {
                RoundedRectangle(cornerRadius: 8)
                    .fill(DesignSystem.Colors.border.opacity(0.3))
                    .frame(width: 40, height: 40)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(height: 16)
                        .frame(maxWidth: 180)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.2))
                        .frame(height: 14)
                        .frame(maxWidth: 120)
                }

                Spacer()
            }

            // 预览按钮骨架
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                .fill(DesignSystem.Colors.border.opacity(0.3))
                .frame(height: 44)
                .frame(maxWidth: 200)
        }
    }
}

// MARK: - 摘要骨架屏

private struct SummarySkeletonView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            // 标题骨架
            RoundedRectangle(cornerRadius: 4)
                .fill(DesignSystem.Colors.border.opacity(0.3))
                .frame(height: 20)
                .frame(maxWidth: 150)

            // 段落骨架
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                ForEach(0..<5, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(height: 16)
                        .frame(maxWidth: index == 4 ? 180 : .infinity)
                }
            }
        }
    }
}

// MARK: - 大纲骨架屏

private struct OutlineSkeletonView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            ForEach(0..<4, id: \.self) { _ in
                HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
                    // 项目符号
                    Circle()
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(width: 6, height: 6)
                        .padding(.top, 6)

                    // 文本内容
                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(DesignSystem.Colors.border.opacity(0.3))
                            .frame(height: 16)
                            .frame(maxWidth: CGFloat.random(in: 120...250))

                        // 子项目（随机显示）
                        if Bool.random() {
                            HStack(spacing: DesignSystem.Spacing.sm) {
                                Spacer().frame(width: 12)
                                Circle()
                                    .fill(DesignSystem.Colors.border.opacity(0.2))
                                    .frame(width: 4, height: 4)
                                    .padding(.top, 6)

                                RoundedRectangle(cornerRadius: 4)
                                    .fill(DesignSystem.Colors.border.opacity(0.2))
                                    .frame(height: 14)
                                    .frame(maxWidth: CGFloat.random(in: 80...180))

                                Spacer()
                            }
                        }
                    }

                    Spacer()
                }
            }
        }
    }
}

// MARK: - 要点骨架屏

private struct KeyPointsSkeletonView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            ForEach(0..<5, id: \.self) { _ in
                HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
                    // 要点图标
                    RoundedRectangle(cornerRadius: 2)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(width: 8, height: 8)
                        .padding(.top, 4)

                    // 要点内容
                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(DesignSystem.Colors.border.opacity(0.3))
                            .frame(height: 16)
                            .frame(maxWidth: CGFloat.random(in: 150...280))

                        // 可能的第二行
                        if Bool.random() {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(DesignSystem.Colors.border.opacity(0.2))
                                .frame(height: 14)
                                .frame(maxWidth: CGFloat.random(in: 100...200))
                        }
                    }

                    Spacer()
                }
            }
        }
    }
}

// MARK: - 试用结束购买按钮视图

private struct TrialEndedPurchaseButtonView: View {


    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            Button {
                Task {
                    await PaymentPopup().present()
                }
            } label: {
                HStack(spacing: DesignSystem.Spacing.sm) {
                    Image(systemName: "crown.fill")
                        .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                        .foregroundColor(.white)

                    Text("立即升级")
                        .font(DesignSystem.Typography.content)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.sm)
                .background(
                    LinearGradient(
                        colors: [DesignSystem.Colors.primary, DesignSystem.Colors.gradient],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(DesignSystem.Rounded.md)
            }
            .buttonStyle(.plain)
        }
        .padding(.top, DesignSystem.Spacing.xs)
    }
}


