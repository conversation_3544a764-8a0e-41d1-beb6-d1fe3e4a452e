import FlowStacks
import MijickPopups
import SwiftUI

// MARK: - 支付弹窗

struct PaymentPopup: BottomPopup {
    var body: some View {
        createContent()
    }

    func createContent() -> some View {
        let screenHeight = UIScreen.main.bounds.height
        let popupHeight = screenHeight * 0.95

        return PaymentView(showNavigationBar: true, onBack: {
            Task {
                await dismissLastPopup()
            }
        })
        .frame(maxWidth: .infinity)
        .frame(height: popupHeight)
        .background(DesignSystem.Colors.membershipPurchaseBackground)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg))
        .ignoresSafeArea(.all, edges: .bottom)
        .persistentSystemOverlays(.hidden)
    }
}
